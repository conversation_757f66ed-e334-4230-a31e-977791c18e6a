[{"timestamp": "2025-06-02 14:05:05", "level": "error", "message": "Uncaught Error: Call to undefined function setUserTheme() in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/profile.php:122", "context": {"exception_class": "Error", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/profile.php", "line": 122, "trace": "#0 {main}", "code": 0}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "**************", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/ai-dashboard/profile.php", "request_method": "POST"}, {"timestamp": "2025-06-02 14:06:51", "level": "error", "message": "Uncaught Error: Call to undefined function setUserTheme() in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/profile.php:122", "context": {"exception_class": "Error", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/profile.php", "line": 122, "trace": "#0 {main}", "code": 0}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "**************", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/ai-dashboard/profile.php", "request_method": "POST"}, {"timestamp": "2025-06-02 15:32:44", "level": "error", "message": "Uncaught Error: Call to undefined function requireLogin() in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/ai_settings.php:15", "context": {"exception_class": "Error", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/ai_settings.php", "line": 15, "trace": "#0 {main}", "code": 0}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "**************", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/ai-dashboard/ai_settings.php", "request_method": "GET"}, {"timestamp": "2025-06-02 16:14:36", "level": "error", "message": "Uncaught Error: Call to undefined function getUserById() in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/includes/theme.php:27", "context": {"exception_class": "Error", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/includes/theme.php", "line": 27, "trace": "#0 /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/includes/theme.php(85): getUserTheme()\n#1 /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/templates/header.php(13): getThemeClasses()\n#2 /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/maintenance.php(18): include('...')\n#3 {main}", "code": 0}, "user_id": "683dcefb1b566", "username": "test", "ip_address": "**************", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/ai-dashboard/maintenance.php", "request_method": "GET"}, {"timestamp": "2025-06-02 16:17:10", "level": "error", "message": "Uncaught Error: Call to undefined function getUserById() in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/includes/theme.php:27", "context": {"exception_class": "Error", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/includes/theme.php", "line": 27, "trace": "#0 /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/includes/theme.php(85): getUserTheme()\n#1 /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/templates/header.php(13): getThemeClasses()\n#2 /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/maintenance.php(18): include('...')\n#3 {main}", "code": 0}, "user_id": "683dcefb1b566", "username": "test", "ip_address": "**************", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/ai-dashboard/maintenance.php", "request_method": "GET"}, {"timestamp": "2025-06-02 16:17:11", "level": "error", "message": "Uncaught Error: Call to undefined function getUserById() in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/includes/theme.php:27", "context": {"exception_class": "Error", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/includes/theme.php", "line": 27, "trace": "#0 /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/includes/theme.php(85): getUserTheme()\n#1 /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/templates/header.php(13): getThemeClasses()\n#2 /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/maintenance.php(18): include('...')\n#3 {main}", "code": 0}, "user_id": "683dcefb1b566", "username": "test", "ip_address": "**************", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/ai-dashboard/maintenance.php", "request_method": "GET"}, {"timestamp": "2025-06-02 16:20:17", "level": "error", "message": "Uncaught Error: Call to undefined function getUserById() in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/includes/theme.php:27", "context": {"exception_class": "Error", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/includes/theme.php", "line": 27, "trace": "#0 /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/includes/theme.php(85): getUserTheme()\n#1 /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/templates/header.php(13): getThemeClasses()\n#2 /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/maintenance.php(18): include('...')\n#3 {main}", "code": 0}, "user_id": "683dcefb1b566", "username": "test", "ip_address": "**************", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/ai-dashboard/maintenance.php", "request_method": "GET"}, {"timestamp": "2025-06-02 16:20:20", "level": "error", "message": "Uncaught Error: Call to undefined function getUserById() in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/includes/theme.php:27", "context": {"exception_class": "Error", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/includes/theme.php", "line": 27, "trace": "#0 /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/includes/theme.php(85): getUserTheme()\n#1 /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/templates/header.php(13): getThemeClasses()\n#2 /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/maintenance.php(18): include('...')\n#3 {main}", "code": 0}, "user_id": "683dcefb1b566", "username": "test", "ip_address": "**************", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/ai-dashboard/maintenance.php", "request_method": "GET"}, {"timestamp": "2025-06-02 21:35:42", "level": "warning", "message": "Warning: require_once(config.php): failed to open stream: No such file or directory in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/includes/update_system.php:14", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/includes/update_system.php", "line": 14}, "user_id": null, "username": "anonymous", "ip_address": "::1", "user_agent": "curl/7.68.0", "request_uri": "/admin/updates.php", "request_method": "GET"}, {"timestamp": "2025-06-02 21:35:42", "level": "error", "message": "Fatal Error: require_once(): Failed opening required 'config.php' (include_path='.:') in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/includes/update_system.php:14", "context": {"type": 64, "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/includes/update_system.php", "line": 14, "fatal": true}, "user_id": null, "username": "anonymous", "ip_address": "::1", "user_agent": "curl/7.68.0", "request_uri": "/admin/updates.php", "request_method": "GET"}, {"timestamp": "2025-06-02 21:36:16", "level": "warning", "message": "Warning: require_once(/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/includes/auth.php): failed to open stream: No such file or directory in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/includes/update_system.php:15", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/includes/update_system.php", "line": 15}, "user_id": null, "username": "anonymous", "ip_address": "::1", "user_agent": "curl/7.68.0", "request_uri": "/admin/updates.php", "request_method": "GET"}, {"timestamp": "2025-06-02 21:36:16", "level": "error", "message": "Fatal Error: require_once(): Failed opening required '/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/includes/auth.php' (include_path='.:') in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/includes/update_system.php:15", "context": {"type": 64, "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/includes/update_system.php", "line": 15, "fatal": true}, "user_id": null, "username": "anonymous", "ip_address": "::1", "user_agent": "curl/7.68.0", "request_uri": "/admin/updates.php", "request_method": "GET"}, {"timestamp": "2025-06-02 21:36:43", "level": "warning", "message": "Warning: require_once(/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/includes/auth.php): failed to open stream: No such file or directory in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/includes/update_system.php:15", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/includes/update_system.php", "line": 15}, "user_id": null, "username": "anonymous", "ip_address": "::1", "user_agent": "curl/7.68.0", "request_uri": "/admin/updates.php", "request_method": "GET"}, {"timestamp": "2025-06-02 21:36:43", "level": "error", "message": "Fatal Error: require_once(): Failed opening required '/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/includes/auth.php' (include_path='.:') in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/includes/update_system.php:15", "context": {"type": 64, "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/includes/update_system.php", "line": 15, "fatal": true}, "user_id": null, "username": "anonymous", "ip_address": "::1", "user_agent": "curl/7.68.0", "request_uri": "/admin/updates.php", "request_method": "GET"}, {"timestamp": "2025-06-02 23:58:07", "level": "warning", "message": "Warning: Trying to access array offset on value of type null in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/dashboard.php:70", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/dashboard.php", "line": 70}, "user_id": "683c90bf0eae3", "username": "eric", "ip_address": "**************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/ai-dashboard/dashboard.php", "request_method": "GET"}, {"timestamp": "2025-06-02 23:58:07", "level": "warning", "message": "Warning: Trying to access array offset on value of type null in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/dashboard.php:83", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/dashboard.php", "line": 83}, "user_id": "683c90bf0eae3", "username": "eric", "ip_address": "**************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/ai-dashboard/dashboard.php", "request_method": "GET"}, {"timestamp": "2025-06-02 23:58:07", "level": "warning", "message": "Warning: Trying to access array offset on value of type null in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/dashboard.php:107", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/dashboard.php", "line": 107}, "user_id": "683c90bf0eae3", "username": "eric", "ip_address": "**************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/ai-dashboard/dashboard.php", "request_method": "GET"}, {"timestamp": "2025-06-02 23:58:07", "level": "warning", "message": "Warning: Trying to access array offset on value of type null in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/dashboard.php:111", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/dashboard.php", "line": 111}, "user_id": "683c90bf0eae3", "username": "eric", "ip_address": "**************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/ai-dashboard/dashboard.php", "request_method": "GET"}, {"timestamp": "2025-06-02 23:58:07", "level": "warning", "message": "Warning: Trying to access array offset on value of type null in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/dashboard.php:129", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/dashboard.php", "line": 129}, "user_id": "683c90bf0eae3", "username": "eric", "ip_address": "**************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/ai-dashboard/dashboard.php", "request_method": "GET"}, {"timestamp": "2025-06-02 23:58:07", "level": "warning", "message": "Warning: Trying to access array offset on value of type null in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/dashboard.php:261", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/dashboard.php", "line": 261}, "user_id": "683c90bf0eae3", "username": "eric", "ip_address": "**************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/ai-dashboard/dashboard.php", "request_method": "GET"}, {"timestamp": "2025-06-02 23:58:07", "level": "warning", "message": "Warning: Trying to access array offset on value of type null in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/dashboard.php:265", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/dashboard.php", "line": 265}, "user_id": "683c90bf0eae3", "username": "eric", "ip_address": "**************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/ai-dashboard/dashboard.php", "request_method": "GET"}, {"timestamp": "2025-06-02 23:58:28", "level": "warning", "message": "Warning: Trying to access array offset on value of type null in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/dashboard.php:70", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/dashboard.php", "line": 70}, "user_id": "683c90bf0eae3", "username": "eric", "ip_address": "**************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/ai-dashboard/dashboard.php", "request_method": "GET"}, {"timestamp": "2025-06-02 23:58:28", "level": "warning", "message": "Warning: Trying to access array offset on value of type null in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/dashboard.php:83", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/dashboard.php", "line": 83}, "user_id": "683c90bf0eae3", "username": "eric", "ip_address": "**************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/ai-dashboard/dashboard.php", "request_method": "GET"}, {"timestamp": "2025-06-02 23:58:28", "level": "warning", "message": "Warning: Trying to access array offset on value of type null in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/dashboard.php:107", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/dashboard.php", "line": 107}, "user_id": "683c90bf0eae3", "username": "eric", "ip_address": "**************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/ai-dashboard/dashboard.php", "request_method": "GET"}, {"timestamp": "2025-06-02 23:58:28", "level": "warning", "message": "Warning: Trying to access array offset on value of type null in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/dashboard.php:111", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/dashboard.php", "line": 111}, "user_id": "683c90bf0eae3", "username": "eric", "ip_address": "**************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/ai-dashboard/dashboard.php", "request_method": "GET"}, {"timestamp": "2025-06-02 23:58:28", "level": "warning", "message": "Warning: Trying to access array offset on value of type null in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/dashboard.php:129", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/dashboard.php", "line": 129}, "user_id": "683c90bf0eae3", "username": "eric", "ip_address": "**************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/ai-dashboard/dashboard.php", "request_method": "GET"}, {"timestamp": "2025-06-02 23:58:28", "level": "warning", "message": "Warning: Trying to access array offset on value of type null in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/dashboard.php:261", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/dashboard.php", "line": 261}, "user_id": "683c90bf0eae3", "username": "eric", "ip_address": "**************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/ai-dashboard/dashboard.php", "request_method": "GET"}, {"timestamp": "2025-06-02 23:58:28", "level": "warning", "message": "Warning: Trying to access array offset on value of type null in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/dashboard.php:265", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/dashboard.php", "line": 265}, "user_id": "683c90bf0eae3", "username": "eric", "ip_address": "**************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/ai-dashboard/dashboard.php", "request_method": "GET"}, {"timestamp": "2025-06-03 09:57:08", "level": "warning", "message": "Warning: Use of undefined constant DB_HOST - assumed 'DB_HOST' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php:27", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php", "line": 27}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/index.php", "request_method": "GET"}, {"timestamp": "2025-06-03 09:57:08", "level": "warning", "message": "Warning: Use of undefined constant DB_NAME - assumed 'DB_NAME' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php:27", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php", "line": 27}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/index.php", "request_method": "GET"}, {"timestamp": "2025-06-03 09:57:08", "level": "warning", "message": "Warning: Use of undefined constant DB_USER - assumed 'DB_USER' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php:28", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php", "line": 28}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/index.php", "request_method": "GET"}, {"timestamp": "2025-06-03 09:57:08", "level": "warning", "message": "Warning: Use of undefined constant DB_PASS - assumed 'DB_PASS' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php:29", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php", "line": 29}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/index.php", "request_method": "GET"}, {"timestamp": "2025-06-03 09:57:08", "level": "error", "message": "Uncaught Exception: Database connection failed: SQLSTATE[HY000] [2002] php_network_getaddresses: getaddrinfo failed: Temporary failure in name resolution in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php:37", "context": {"exception_class": "Exception", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php", "line": 37, "trace": "#0 /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php(15): SampleAppDatabase->initializeConnection()\n#1 /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/app.php(24): SampleAppDatabase->__construct()\n#2 /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/app.php(311): SampleApp->__construct()\n#3 /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/index.php(18): require_once('/home/<USER>/...')\n#4 {main}", "code": 0}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/index.php", "request_method": "GET"}, {"timestamp": "2025-06-03 09:57:46", "level": "warning", "message": "Warning: Use of undefined constant DB_HOST - assumed 'DB_HOST' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php:27", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php", "line": 27}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/templates/chat.php", "request_method": "GET"}, {"timestamp": "2025-06-03 09:57:46", "level": "warning", "message": "Warning: Use of undefined constant DB_NAME - assumed 'DB_NAME' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php:27", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php", "line": 27}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/templates/chat.php", "request_method": "GET"}, {"timestamp": "2025-06-03 09:57:46", "level": "warning", "message": "Warning: Use of undefined constant DB_USER - assumed 'DB_USER' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php:28", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php", "line": 28}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/templates/chat.php", "request_method": "GET"}, {"timestamp": "2025-06-03 09:57:46", "level": "warning", "message": "Warning: Use of undefined constant DB_PASS - assumed 'DB_PASS' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php:29", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php", "line": 29}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/templates/chat.php", "request_method": "GET"}, {"timestamp": "2025-06-03 09:57:46", "level": "error", "message": "Uncaught Exception: Database connection failed: SQLSTATE[HY000] [2002] php_network_getaddresses: getaddrinfo failed: Temporary failure in name resolution in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php:37", "context": {"exception_class": "Exception", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php", "line": 37, "trace": "#0 /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php(15): SampleAppDatabase->initializeConnection()\n#1 /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/app.php(24): SampleAppDatabase->__construct()\n#2 /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/app.php(311): SampleApp->__construct()\n#3 /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/templates/chat.php(18): require_once('/home/<USER>/...')\n#4 {main}", "code": 0}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/templates/chat.php", "request_method": "GET"}, {"timestamp": "2025-06-03 10:03:10", "level": "warning", "message": "Warning: Use of undefined constant DB_HOST - assumed 'DB_HOST' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php:27", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php", "line": 27}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/", "request_method": "GET"}, {"timestamp": "2025-06-03 10:03:10", "level": "warning", "message": "Warning: Use of undefined constant DB_NAME - assumed 'DB_NAME' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php:27", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php", "line": 27}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/", "request_method": "GET"}, {"timestamp": "2025-06-03 10:03:10", "level": "warning", "message": "Warning: Use of undefined constant DB_USER - assumed 'DB_USER' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php:28", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php", "line": 28}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/", "request_method": "GET"}, {"timestamp": "2025-06-03 10:03:10", "level": "warning", "message": "Warning: Use of undefined constant DB_PASS - assumed 'DB_PASS' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php:29", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php", "line": 29}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/", "request_method": "GET"}, {"timestamp": "2025-06-03 10:03:10", "level": "error", "message": "Uncaught Exception: Database connection failed: SQLSTATE[HY000] [2002] php_network_getaddresses: getaddrinfo failed: Temporary failure in name resolution in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php:37", "context": {"exception_class": "Exception", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php", "line": 37, "trace": "#0 /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php(15): SampleAppDatabase->initializeConnection()\n#1 /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/app.php(24): SampleAppDatabase->__construct()\n#2 /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/app.php(311): SampleApp->__construct()\n#3 /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/index.php(18): require_once('/home/<USER>/...')\n#4 {main}", "code": 0}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/", "request_method": "GET"}, {"timestamp": "2025-06-03 10:06:31", "level": "warning", "message": "Warning: Use of undefined constant DB_HOST - assumed 'DB_HOST' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php:27", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php", "line": 27}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/test.php", "request_method": "GET"}, {"timestamp": "2025-06-03 10:06:31", "level": "warning", "message": "Warning: Use of undefined constant DB_NAME - assumed 'DB_NAME' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php:27", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php", "line": 27}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/test.php", "request_method": "GET"}, {"timestamp": "2025-06-03 10:06:31", "level": "warning", "message": "Warning: Use of undefined constant DB_USER - assumed 'DB_USER' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php:28", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php", "line": 28}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/test.php", "request_method": "GET"}, {"timestamp": "2025-06-03 10:06:31", "level": "warning", "message": "Warning: Use of undefined constant DB_PASS - assumed 'DB_PASS' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php:29", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php", "line": 29}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/test.php", "request_method": "GET"}, {"timestamp": "2025-06-03 10:09:19", "level": "warning", "message": "Warning: Use of undefined constant DB_HOST - assumed 'DB_HOST' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php:27", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php", "line": 27}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/test.php", "request_method": "GET"}, {"timestamp": "2025-06-03 10:09:19", "level": "warning", "message": "Warning: Use of undefined constant DB_NAME - assumed 'DB_NAME' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php:27", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php", "line": 27}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/test.php", "request_method": "GET"}, {"timestamp": "2025-06-03 10:09:19", "level": "warning", "message": "Warning: Use of undefined constant DB_USER - assumed 'DB_USER' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php:28", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php", "line": 28}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/test.php", "request_method": "GET"}, {"timestamp": "2025-06-03 10:09:19", "level": "warning", "message": "Warning: Use of undefined constant DB_PASS - assumed 'DB_PASS' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php:29", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php", "line": 29}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/test.php", "request_method": "GET"}, {"timestamp": "2025-06-03 10:09:40", "level": "warning", "message": "Warning: Use of undefined constant DB_HOST - assumed 'DB_HOST' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php:27", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php", "line": 27}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/index.php", "request_method": "GET"}, {"timestamp": "2025-06-03 10:09:40", "level": "warning", "message": "Warning: Use of undefined constant DB_NAME - assumed 'DB_NAME' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php:27", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php", "line": 27}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/index.php", "request_method": "GET"}, {"timestamp": "2025-06-03 10:09:40", "level": "warning", "message": "Warning: Use of undefined constant DB_USER - assumed 'DB_USER' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php:28", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php", "line": 28}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/index.php", "request_method": "GET"}, {"timestamp": "2025-06-03 10:09:40", "level": "warning", "message": "Warning: Use of undefined constant DB_PASS - assumed 'DB_PASS' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php:29", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php", "line": 29}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/index.php", "request_method": "GET"}, {"timestamp": "2025-06-03 10:10:21", "level": "warning", "message": "Warning: Use of undefined constant DB_HOST - assumed 'DB_HOST' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php:27", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php", "line": 27}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/index.php?route=/analytics", "request_method": "GET"}, {"timestamp": "2025-06-03 10:10:21", "level": "warning", "message": "Warning: Use of undefined constant DB_NAME - assumed 'DB_NAME' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php:27", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php", "line": 27}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/index.php?route=/analytics", "request_method": "GET"}, {"timestamp": "2025-06-03 10:10:21", "level": "warning", "message": "Warning: Use of undefined constant DB_USER - assumed 'DB_USER' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php:28", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php", "line": 28}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/index.php?route=/analytics", "request_method": "GET"}, {"timestamp": "2025-06-03 10:10:21", "level": "warning", "message": "Warning: Use of undefined constant DB_PASS - assumed 'DB_PASS' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php:29", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php", "line": 29}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/index.php?route=/analytics", "request_method": "GET"}, {"timestamp": "2025-06-03 10:10:28", "level": "warning", "message": "Warning: Use of undefined constant DB_HOST - assumed 'DB_HOST' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php:27", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php", "line": 27}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/index.php?route=/chat", "request_method": "GET"}, {"timestamp": "2025-06-03 10:10:28", "level": "warning", "message": "Warning: Use of undefined constant DB_NAME - assumed 'DB_NAME' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php:27", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php", "line": 27}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/index.php?route=/chat", "request_method": "GET"}, {"timestamp": "2025-06-03 10:10:28", "level": "warning", "message": "Warning: Use of undefined constant DB_USER - assumed 'DB_USER' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php:28", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php", "line": 28}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/index.php?route=/chat", "request_method": "GET"}, {"timestamp": "2025-06-03 10:10:28", "level": "warning", "message": "Warning: Use of undefined constant DB_PASS - assumed 'DB_PASS' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php:29", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/includes/database.php", "line": 29}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/index.php?route=/chat", "request_method": "GET"}, {"timestamp": "2025-06-03 10:12:17", "level": "warning", "message": "Warning: Use of undefined constant DB_HOST - assumed 'DB_HOST' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/simple.php:51", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/simple.php", "line": 51}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/simple.php", "request_method": "GET"}, {"timestamp": "2025-06-03 10:12:17", "level": "warning", "message": "Warning: Use of undefined constant DB_NAME - assumed 'DB_NAME' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/simple.php:51", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/simple.php", "line": 51}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/simple.php", "request_method": "GET"}, {"timestamp": "2025-06-03 10:12:17", "level": "warning", "message": "Warning: Use of undefined constant DB_USER - assumed 'DB_USER' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/simple.php:52", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/simple.php", "line": 52}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/simple.php", "request_method": "GET"}, {"timestamp": "2025-06-03 10:12:17", "level": "warning", "message": "Warning: Use of undefined constant DB_PASS - assumed 'DB_PASS' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/simple.php:53", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/simple.php", "line": 53}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/simple.php", "request_method": "GET"}, {"timestamp": "2025-06-03 10:16:04", "level": "warning", "message": "Warning: Use of undefined constant DB_HOST - assumed 'DB_HOST' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/simple.php:51", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/simple.php", "line": 51}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/simple.php", "request_method": "GET"}, {"timestamp": "2025-06-03 10:16:04", "level": "warning", "message": "Warning: Use of undefined constant DB_NAME - assumed 'DB_NAME' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/simple.php:51", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/simple.php", "line": 51}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/simple.php", "request_method": "GET"}, {"timestamp": "2025-06-03 10:16:04", "level": "warning", "message": "Warning: Use of undefined constant DB_USER - assumed 'DB_USER' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/simple.php:52", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/simple.php", "line": 52}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/simple.php", "request_method": "GET"}, {"timestamp": "2025-06-03 10:16:04", "level": "warning", "message": "Warning: Use of undefined constant DB_PASS - assumed 'DB_PASS' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/simple.php:53", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/simple.php", "line": 53}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/simple.php", "request_method": "GET"}, {"timestamp": "2025-06-03 10:16:04", "level": "warning", "message": "Warning: Use of undefined constant DB_HOST - assumed 'DB_HOST' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/simple.php:51", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/simple.php", "line": 51}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/simple.php", "request_method": "GET"}, {"timestamp": "2025-06-03 10:16:04", "level": "warning", "message": "Warning: Use of undefined constant DB_NAME - assumed 'DB_NAME' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/simple.php:51", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/simple.php", "line": 51}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/simple.php", "request_method": "GET"}, {"timestamp": "2025-06-03 10:16:04", "level": "warning", "message": "Warning: Use of undefined constant DB_USER - assumed 'DB_USER' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/simple.php:52", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/simple.php", "line": 52}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/simple.php", "request_method": "GET"}, {"timestamp": "2025-06-03 10:16:04", "level": "warning", "message": "Warning: Use of undefined constant DB_PASS - assumed 'DB_PASS' (this will throw an Error in a future version of PHP) in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/simple.php:53", "context": {"severity": 2, "error_type": "Warning", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/sample-app/simple.php", "line": 53}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/app/sample-app/simple.php", "request_method": "GET"}, {"timestamp": "2025-06-03 16:01:41", "level": "error", "message": "Uncaught ParseError: syntax error, unexpected token \"<\", expecting \"elseif\" or \"else\" or \"endif\" in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/templates/header.php:210", "context": {"exception_class": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/templates/header.php", "line": 210, "trace": "#0 {main}", "code": 0}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "**************", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/ai-dashboard/admin/settings.php", "request_method": "GET"}, {"timestamp": "2025-06-07 12:09:31", "level": "error", "message": "Uncaught Error: Call to undefined method ChatBotApp::getAIIntegration() in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/chat-bot/mini-apps/notes.php:291", "context": {"exception_class": "Error", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/chat-bot/mini-apps/notes.php", "line": 291, "trace": "#0 /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/chat-bot/mini-apps/notes.php(190): makeChatIntegrationRequest()\n#1 /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/chat-bot/mini-apps/notes.php(42): sendToChat()\n#2 {main}", "code": 0}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "**************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/ai-dashboard/app/chat-bot/mini-apps/notes.php", "request_method": "POST"}, {"timestamp": "2025-06-07 13:09:47", "level": "error", "message": "Uncaught Error: Call to undefined function getCurrentTheme() in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/chat-bot/templates/mini-apps.php:37", "context": {"exception_class": "Error", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/chat-bot/templates/mini-apps.php", "line": 37, "trace": "#0 /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/chat-bot/index.php(30): include()\n#1 {main}", "code": 0}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "**************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/ai-dashboard/app/chat-bot/index.php?route=/mini-apps&popup=true", "request_method": "GET"}, {"timestamp": "2025-06-07 13:28:12", "level": "error", "message": "Uncaught Error: Call to undefined function getCurrentTheme() in /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/chat-bot/templates/mini-apps.php:42", "context": {"exception_class": "Error", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/chat-bot/templates/mini-apps.php", "line": 42, "trace": "#0 /home/<USER>/webapps/cobalt-sandbox/ai-dashboard/app/chat-bot/index.php(30): include()\n#1 {main}", "code": 0}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "**************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/ai-dashboard/app/chat-bot/index.php?route=/mini-apps&popup=true", "request_method": "GET"}, {"timestamp": "2025-06-13 23:24:26", "level": "error", "message": "Uncaught ArgumentCountError: Too few arguments to function AIClient::__construct(), 0 passed in /home/<USER>/webapps/cobalt-sandbox/ai-story-builder/app/generative-story-bible/includes/ai-integration.php on line 25 and exactly 1 expected in /home/<USER>/webapps/cobalt-sandbox/ai-story-builder/includes/ai_client.php:16", "context": {"exception_class": "ArgumentCountError", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-story-builder/includes/ai_client.php", "line": 16, "trace": "#0 /home/<USER>/webapps/cobalt-sandbox/ai-story-builder/app/generative-story-bible/includes/ai-integration.php(25): AIClient->__construct()\n#1 /home/<USER>/webapps/cobalt-sandbox/ai-story-builder/app/generative-story-bible/includes/ai-integration.php(15): StoryBibleAIIntegration->initializeAIClient()\n#2 /home/<USER>/webapps/cobalt-sandbox/ai-story-builder/app/generative-story-bible/includes/app.php(22): StoryBibleAIIntegration->__construct()\n#3 /home/<USER>/webapps/cobalt-sandbox/ai-story-builder/app/generative-story-bible/includes/app.php(266): GenerativeStoryBibleApp->__construct()\n#4 /home/<USER>/webapps/cobalt-sandbox/ai-story-builder/app/generative-story-bible/index.php(18): require_once('...')\n#5 {main}", "code": 0}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "**************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/ai-story-builder/app/generative-story-bible/index.php", "request_method": "GET"}, {"timestamp": "2025-06-13 23:26:02", "level": "error", "message": "Uncaught ArgumentCountError: Too few arguments to function AIClient::__construct(), 0 passed in /home/<USER>/webapps/cobalt-sandbox/ai-story-builder/app/generative-story-bible/includes/ai-integration.php on line 25 and exactly 1 expected in /home/<USER>/webapps/cobalt-sandbox/ai-story-builder/includes/ai_client.php:16", "context": {"exception_class": "ArgumentCountError", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-story-builder/includes/ai_client.php", "line": 16, "trace": "#0 /home/<USER>/webapps/cobalt-sandbox/ai-story-builder/app/generative-story-bible/includes/ai-integration.php(25): AIClient->__construct()\n#1 /home/<USER>/webapps/cobalt-sandbox/ai-story-builder/app/generative-story-bible/includes/ai-integration.php(15): StoryBibleAIIntegration->initializeAIClient()\n#2 /home/<USER>/webapps/cobalt-sandbox/ai-story-builder/app/generative-story-bible/includes/app.php(22): StoryBibleAIIntegration->__construct()\n#3 /home/<USER>/webapps/cobalt-sandbox/ai-story-builder/app/generative-story-bible/includes/app.php(266): GenerativeStoryBibleApp->__construct()\n#4 /home/<USER>/webapps/cobalt-sandbox/ai-story-builder/app/generative-story-bible/index.php(18): require_once('...')\n#5 {main}", "code": 0}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "**************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/ai-story-builder/app/generative-story-bible/index.php", "request_method": "GET"}, {"timestamp": "2025-06-13 23:31:56", "level": "error", "message": "Uncaught ArgumentCountError: Too few arguments to function AIClient::__construct(), 0 passed in /home/<USER>/webapps/cobalt-sandbox/ai-story-builder/app/generative-story-bible/includes/ai-integration.php on line 25 and exactly 1 expected in /home/<USER>/webapps/cobalt-sandbox/ai-story-builder/includes/ai_client.php:16", "context": {"exception_class": "ArgumentCountError", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-story-builder/includes/ai_client.php", "line": 16, "trace": "#0 /home/<USER>/webapps/cobalt-sandbox/ai-story-builder/app/generative-story-bible/includes/ai-integration.php(25): AIClient->__construct()\n#1 /home/<USER>/webapps/cobalt-sandbox/ai-story-builder/app/generative-story-bible/includes/ai-integration.php(15): StoryBibleAIIntegration->initializeAIClient()\n#2 /home/<USER>/webapps/cobalt-sandbox/ai-story-builder/app/generative-story-bible/includes/app.php(22): StoryBibleAIIntegration->__construct()\n#3 /home/<USER>/webapps/cobalt-sandbox/ai-story-builder/app/generative-story-bible/includes/app.php(266): GenerativeStoryBibleApp->__construct()\n#4 /home/<USER>/webapps/cobalt-sandbox/ai-story-builder/app/generative-story-bible/index.php(18): require_once('...')\n#5 {main}", "code": 0}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "**************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/ai-story-builder/app/generative-story-bible/index.php", "request_method": "GET"}, {"timestamp": "2025-06-13 23:38:04", "level": "error", "message": "Uncaught ArgumentCountError: Too few arguments to function AIClient::__construct(), 0 passed in /home/<USER>/webapps/cobalt-sandbox/ai-story-builder/app/generative-story-bible/includes/ai-integration.php on line 25 and exactly 1 expected in /home/<USER>/webapps/cobalt-sandbox/ai-story-builder/includes/ai_client.php:16", "context": {"exception_class": "ArgumentCountError", "file": "/home/<USER>/webapps/cobalt-sandbox/ai-story-builder/includes/ai_client.php", "line": 16, "trace": "#0 /home/<USER>/webapps/cobalt-sandbox/ai-story-builder/app/generative-story-bible/includes/ai-integration.php(25): AIClient->__construct()\n#1 /home/<USER>/webapps/cobalt-sandbox/ai-story-builder/app/generative-story-bible/includes/ai-integration.php(15): StoryBibleAIIntegration->initializeAIClient()\n#2 /home/<USER>/webapps/cobalt-sandbox/ai-story-builder/app/generative-story-bible/includes/app.php(22): StoryBibleAIIntegration->__construct()\n#3 /home/<USER>/webapps/cobalt-sandbox/ai-story-builder/app/generative-story-bible/includes/app.php(266): GenerativeStoryBibleApp->__construct()\n#4 /home/<USER>/webapps/cobalt-sandbox/ai-story-builder/app/generative-story-bible/index.php(18): require_once('...')\n#5 {main}", "code": 0}, "user_id": "683dcafa5ce49", "username": "eric", "ip_address": "**************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "request_uri": "/ai-story-builder/app/generative-story-bible/index.php", "request_method": "GET"}]