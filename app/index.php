<?php
/**
 * AI Dashboard Apps Directory
 * 
 * This page shows available apps in the AI Dashboard system
 */

// Get the current directory
$appsDir = __DIR__;
$apps = [];

// Scan for app directories
if (is_dir($appsDir)) {
    $directories = array_filter(glob($appsDir . '/*'), 'is_dir');
    
    foreach ($directories as $dir) {
        $appName = basename($dir);
        $configFile = $dir . '/config.json';
        
        if (file_exists($configFile)) {
            $config = json_decode(file_get_contents($configFile), true);
            if ($config && isset($config['app'])) {
                $apps[] = [
                    'id' => $appName,
                    'name' => $config['app']['name'] ?? $appName,
                    'description' => $config['app']['description'] ?? 'No description available',
                    'version' => $config['app']['version'] ?? '1.0.0',
                    'status' => $config['app']['status'] ?? 'unknown',
                    'path' => $appName
                ];
            }
        }
    }
}

// If only one app exists, redirect to it
if (count($apps) === 1) {
    header('Location: ' . $apps[0]['path'] . '/');
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Dashboard - Apps</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="bg-gray-50 dark:bg-gray-900">
    <div class="min-h-screen py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-4xl mx-auto">
            <!-- Header -->
            <div class="text-center mb-12">
                <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                    AI Dashboard Apps
                </h1>
                <p class="text-lg text-gray-600 dark:text-gray-300">
                    Available applications in your AI Dashboard
                </p>
            </div>

            <?php if (empty($apps)): ?>
                <!-- No Apps Found -->
                <div class="text-center py-12">
                    <div class="text-6xl mb-4">📱</div>
                    <h2 class="text-2xl font-semibold text-gray-900 dark:text-white mb-2">No Apps Found</h2>
                    <p class="text-gray-600 dark:text-gray-300 mb-6">
                        No configured apps were found in the apps directory.
                    </p>
                    <div class="bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-lg p-4 max-w-md mx-auto">
                        <p class="text-sm text-blue-800 dark:text-blue-200">
                            To add apps, place them in the <code class="bg-blue-100 dark:bg-blue-800 px-1 rounded">/app/</code> directory with a valid <code class="bg-blue-100 dark:bg-blue-800 px-1 rounded">config.json</code> file.
                        </p>
                    </div>
                </div>
            <?php else: ?>
                <!-- Apps Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <?php foreach ($apps as $app): ?>
                        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 border border-gray-200 dark:border-gray-700">
                            <div class="p-6">
                                <!-- App Header -->
                                <div class="flex items-start justify-between mb-4">
                                    <div class="flex-1">
                                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-1">
                                            <?php echo htmlspecialchars($app['name']); ?>
                                        </h3>
                                        <div class="flex items-center space-x-2">
                                            <span class="text-xs px-2 py-1 rounded-full <?php 
                                                echo $app['status'] === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 
                                                    ($app['status'] === 'development' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' : 
                                                    'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'); 
                                            ?>">
                                                <?php echo htmlspecialchars($app['status']); ?>
                                            </span>
                                            <span class="text-xs text-gray-500 dark:text-gray-400">
                                                v<?php echo htmlspecialchars($app['version']); ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <!-- App Description -->
                                <p class="text-gray-600 dark:text-gray-300 text-sm mb-6 line-clamp-3">
                                    <?php echo htmlspecialchars($app['description']); ?>
                                </p>

                                <!-- App Actions -->
                                <div class="flex space-x-3">
                                    <a href="<?php echo htmlspecialchars($app['path']); ?>/" 
                                       class="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-4 rounded-lg font-medium transition duration-200">
                                        Open App
                                    </a>
                                    <?php if (file_exists(__DIR__ . '/' . $app['path'] . '/README.md')): ?>
                                        <a href="<?php echo htmlspecialchars($app['path']); ?>/README.md" 
                                           class="bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 py-2 px-4 rounded-lg font-medium transition duration-200">
                                            Docs
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Footer Info -->
                <div class="mt-12 text-center">
                    <p class="text-sm text-gray-500 dark:text-gray-400">
                        Found <?php echo count($apps); ?> app<?php echo count($apps) !== 1 ? 's' : ''; ?> in the dashboard
                    </p>
                </div>
            <?php endif; ?>

            <!-- Back to Dashboard -->
            <div class="mt-8 text-center">
                <a href="../" class="inline-flex items-center text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium">
                    ← Back to Dashboard
                </a>
            </div>
        </div>
    </div>
</body>
</html>
