# Firebase Setup Guide for Generative Story Bible

This guide will help you set up Firebase integration for the Generative Story Bible app.

## Prerequisites

- A Google account
- Access to the Firebase Console

## Step 1: Create a Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project" or "Add project"
3. Enter a project name (e.g., "story-bible-app")
4. Choose whether to enable Google Analytics (optional)
5. Click "Create project"

## Step 2: Add a Web App

1. In your Firebase project dashboard, click the web icon (`</>`)
2. Register your app with a nickname (e.g., "Generative Story Bible")
3. **Do not** check "Also set up Firebase Hosting" for now
4. Click "Register app"
5. Copy the Firebase configuration object

## Step 3: Configure the App

1. Open `assets/js/firebase-config.js` in your app directory
2. Replace the placeholder values with your actual Firebase configuration:

```javascript
export const firebaseConfig = {
    apiKey: "your-actual-api-key",
    authDomain: "your-project-id.firebaseapp.com",
    projectId: "your-actual-project-id",
    storageBucket: "your-project-id.appspot.com",
    messagingSenderId: "123456789012",
    appId: "1:123456789012:web:abcdef123456789012345678"
};
```

## Step 4: Enable Firestore Database

1. In the Firebase Console, go to "Firestore Database"
2. Click "Create database"
3. Choose "Start in test mode" (for development)
4. Select a location for your database
5. Click "Done"

## Step 5: Enable Authentication

1. In the Firebase Console, go to "Authentication"
2. Click "Get started"
3. Go to the "Sign-in method" tab
4. Enable "Anonymous" authentication
5. Click "Save"

## Step 6: Set Up Security Rules

For development, you can use these Firestore security rules:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow read/write access to artifacts collection for authenticated users
    match /artifacts/{appId}/users/{userId}/{document=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
  }
}
```

To apply these rules:
1. Go to "Firestore Database" in the Firebase Console
2. Click on the "Rules" tab
3. Replace the existing rules with the rules above
4. Click "Publish"

## Step 7: Test the Integration

1. Save your changes to `firebase-config.js`
2. Reload your Generative Story Bible app
3. Open the browser's developer console
4. You should see messages like:
   - "Firebase initialized successfully"
   - "Anonymous sign-in successful"
   - "User authenticated: [user-id]"
   - "Bricks loaded from Firestore: []"

## Data Structure

The app stores data in Firestore with this structure:

```
artifacts/
  └── generative-story-bible/
      └── users/
          └── [user-id]/
              └── bricks/
                  ├── [brick-id-1]
                  ├── [brick-id-2]
                  └── ...
```

Each brick document contains:
- `title`: String
- `content`: String
- `type`: String (character, setting, plot, etc.)
- `tags`: Array of strings
- `created_at`: Timestamp
- `updated_at`: Timestamp

## Troubleshooting

### Firebase not initializing
- Check that your API key and project ID are correct
- Ensure your domain is authorized in Firebase Console > Authentication > Settings > Authorized domains

### Authentication failing
- Verify that Anonymous authentication is enabled
- Check the browser console for specific error messages

### Firestore permission denied
- Ensure your security rules allow access for authenticated users
- Verify that the user is properly authenticated before accessing Firestore

### Sample data mode
If Firebase is not configured, the app will automatically fall back to sample data mode with demo bricks.

## Production Considerations

For production deployment:

1. **Security Rules**: Implement more restrictive security rules
2. **API Key Restrictions**: Restrict your API key to specific domains
3. **Monitoring**: Set up Firebase monitoring and alerts
4. **Backup**: Implement regular Firestore backups
5. **Performance**: Consider Firestore query optimization for large datasets

## Support

If you encounter issues:
1. Check the browser console for error messages
2. Verify your Firebase configuration
3. Ensure all Firebase services are enabled
4. Review the Firebase documentation at https://firebase.google.com/docs
