<?php
/**
 * Generative Story Bible - Main Entry Point
 *
 * This is the main entry point for the Generative Story Bible app,
 * providing AI-powered story generation using building blocks (Bricks).
 */

// Include dashboard configuration and authentication
require_once __DIR__ . '/../../config.php';
require_once __DIR__ . '/../../auth.php';
require_once __DIR__ . '/../../includes/theme.php';

// Require authentication
requireAuth();

// Include app-specific files
require_once __DIR__ . '/includes/app.php';

// Get the app instance
$app = $GLOBALS['generative_story_bible_app'];
$config = $app->getConfig();

// Handle route - default to main interface
$route = $_GET['route'] ?? '/';
$templateFile = $app->handleRoute($route);

// If route returns a different template, include it instead
if ($templateFile !== $app->getAppPath() . '/index.php') {
    include $templateFile;
    exit;
}

// If we're on the default route, show main interface
if ($route === '/') {
    include $app->getAppPath() . '/templates/main.php';
    exit;
}

// Set page title
$pageTitle = $config['app']['name'];

// Include dashboard header
include __DIR__ . '/../../templates/header.php';
?>

<div class="space-y-6">
    <!-- App Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold <?php echo getThemeComponentClasses('text-primary'); ?>">
                <?php echo getIcon('book-story', 'text-3xl'); ?> <?php echo htmlspecialchars($config['app']['name']); ?>
            </h1>
            <p class="<?php echo getThemeComponentClasses('text-secondary'); ?> mt-1">
                <?php echo htmlspecialchars($config['app']['description']); ?>
            </p>
        </div>
        <div class="flex space-x-3">
            <a href="../../dashboard.php" class="<?php echo getThemeComponentClasses('button-secondary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200">
                Back to Dashboard
            </a>
            <a href="?route=/settings" class="<?php echo getThemeComponentClasses('button-secondary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200">
                Settings
            </a>
        </div>
    </div>

    <!-- App Status -->
    <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow mb-6">
        <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?>">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">App Status</h2>
        </div>
        
        <div class="p-6">
            <?php
            $status = $app->getStatus();
            $health = $status['health'];
            $healthColor = $health['status'] === 'healthy' ? 'green' : ($health['status'] === 'unhealthy' ? 'red' : 'yellow');
            ?>
            
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-<?php echo $healthColor; ?>-600 dark:text-<?php echo $healthColor; ?>-400">
                        <?php echo ucfirst($health['status']); ?>
                    </div>
                    <div class="text-sm text-gray-600 dark:text-gray-300">Health Status</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600 dark:text-blue-400"><?php echo $status['version']; ?></div>
                    <div class="text-sm text-gray-600 dark:text-gray-300">Version</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                        <?php echo count($status['features']); ?>
                    </div>
                    <div class="text-sm text-gray-600 dark:text-gray-300">Features</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">
                        <?php echo date('H:i', strtotime($status['last_activity'])); ?>
                    </div>
                    <div class="text-sm text-gray-600 dark:text-gray-300">Last Activity</div>
                </div>
            </div>
            
            <!-- Health Checks -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <?php foreach ($health['checks'] as $check => $result): ?>
                    <div class="flex items-center space-x-3">
                        <div class="text-lg">
                            <?php
                            if ($result === 'ok') {
                                echo getIcon('success-check', 'text-lg');
                            } elseif ($result === 'error') {
                                echo getIcon('error-x', 'text-lg');
                            } else {
                                echo getIcon('warning-alert', 'text-lg');
                            }
                            ?>
                        </div>
                        <div>
                            <div class="font-medium text-gray-900 dark:text-white"><?php echo ucfirst(str_replace('_', ' ', $check)); ?></div>
                            <div class="text-sm text-gray-600 dark:text-gray-300"><?php echo ucfirst($result); ?></div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <!-- App Features -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
        <!-- Story Builder -->
        <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow">
            <div class="p-6 flex flex-col h-full">
                <div class="flex items-center space-x-3 mb-4">
                    <div class="text-2xl"><?php echo getIcon('book-story', 'text-2xl'); ?></div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Story Builder</h3>
                </div>
                <p class="text-gray-600 dark:text-gray-300 mb-4 flex-grow">
                    Create compelling stories by combining story elements (Bricks) with AI-powered generation.
                </p>
                <a href="?route=/" class="<?php echo getThemeComponentClasses('button-primary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200 w-full text-center block mt-auto">
                    Open Builder
                </a>
            </div>
        </div>

        <!-- Brick Library -->
        <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow">
            <div class="p-6 flex flex-col h-full">
                <div class="flex items-center space-x-3 mb-4">
                    <div class="text-2xl"><?php echo getIcon('file-storage', 'text-2xl'); ?></div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Brick Library</h3>
                </div>
                <p class="text-gray-600 dark:text-gray-300 mb-4 flex-grow">
                    Manage your collection of story elements including characters, settings, and plot devices.
                </p>
                <a href="?route=/bricks" class="<?php echo getThemeComponentClasses('button-primary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200 w-full text-center block mt-auto">
                    Manage Bricks
                </a>
            </div>
        </div>

        <!-- Story Archive -->
        <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow">
            <div class="p-6 flex flex-col h-full">
                <div class="flex items-center space-x-3 mb-4">
                    <div class="text-2xl"><?php echo getIcon('activity-chart', 'text-2xl'); ?></div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Story Archive</h3>
                </div>
                <p class="text-gray-600 dark:text-gray-300 mb-4 flex-grow">
                    View and manage your generated stories, analyze patterns, and track your creative progress.
                </p>
                <a href="?route=/stories" class="<?php echo getThemeComponentClasses('button-primary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200 w-full text-center block mt-auto">
                    View Stories
                </a>
            </div>
        </div>
    </div>

    <!-- Integration Info -->
    <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow">
        <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?>">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">About Generative Story Bible</h2>
        </div>
        
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Features -->
                <div>
                    <h3 class="font-semibold text-gray-900 dark:text-white mb-3">Key Features</h3>
                    <ul class="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                        <li>✅ Drag & drop story building interface</li>
                        <li>✅ AI-powered story generation</li>
                        <li>✅ Reusable story elements (Bricks)</li>
                        <li>✅ Collaborative story development</li>
                        <li>✅ Story analysis and feedback</li>
                        <li>✅ Export and sharing capabilities</li>
                    </ul>
                </div>

                <!-- Technical Details -->
                <div>
                    <h3 class="font-semibold text-gray-900 dark:text-white mb-3">Technical Integration</h3>
                    <ul class="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                        <li>✅ Dashboard authentication integration</li>
                        <li>✅ AI API configuration sharing</li>
                        <li>✅ Flat file data storage</li>
                        <li>✅ Responsive Tailwind CSS design</li>
                        <li>✅ Modern ES6 JavaScript modules</li>
                        <li>✅ Firebase Firestore ready</li>
                    </ul>
                </div>
            </div>
            
            <div class="mt-6 p-4 bg-blue-50 dark:bg-blue-900 rounded-lg">
                <h4 class="font-medium text-blue-900 dark:text-blue-100 mb-2"><?php echo getIcon('rocket-deploy', 'text-base'); ?> Development Notes</h4>
                <p class="text-sm text-blue-800 dark:text-blue-200">
                    The Generative Story Bible provides a collaborative platform for building and managing story elements with AI-powered generation.
                    It demonstrates advanced drag & drop interfaces, modular story construction, and creative AI integration patterns.
                </p>
            </div>
        </div>
    </div>
</div>

<!-- App-specific JavaScript -->
<script>
// App initialization
document.addEventListener('DOMContentLoaded', function() {
    console.log('Generative Story Bible initialized');
    
    // Log app activity
    fetch('api/activity.php', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            action: 'app.viewed',
            data: { route: '<?php echo htmlspecialchars($route); ?>' }
        })
    }).catch(err => console.log('Activity logging failed:', err));
});
</script>

<?php include __DIR__ . '/../../templates/footer.php'; ?>
