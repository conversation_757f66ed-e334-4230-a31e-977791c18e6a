# Testing Guide for Phase 3: Brick Creation and Display

This guide helps you test the new brick creation functionality implemented in Phase 3.

## Features to Test

### 1. Modal Functionality

**Opening the Modal:**
- Click the "+ New Brick" button in the left panel
- <PERSON><PERSON> should appear with a semi-transparent overlay
- Form should be focused on the "Brick Name" field

**Closing the Modal:**
- Click the "Cancel" button → <PERSON>dal should close
- Click outside the modal (on the overlay) → <PERSON><PERSON> should close
- Press the ESC key → Modal should close
- Form fields should be cleared when modal closes

### 2. Form Validation

**Required Fields:**
- Try submitting with empty fields → Should show "Please fill in all fields" alert
- All four fields are required:
  - Brick Name
  - Character's Archetype
  - Character's Backstory
  - Character's Goal

**Field Behavior:**
- Focus states should show blue borders
- Valid fields should show green borders
- Invalid fields should show red borders

### 3. Brick Creation

**Sample Data to Test:**
```
Brick Name: Elara Moonwhisper
Character's Archetype: Wise Mentor
Character's Backstory: A former court wizard who was exiled for refusing to use dark magic. She now lives in the forest, helping young adventurers discover their true potential.
Character's Goal: To train the next generation of heroes while seeking redemption for her past mistakes.
```

**Expected Behavior:**
- Click "Save Brick" → <PERSON><PERSON> should show "Saving..." temporarily
- <PERSON>dal should close automatically after saving
- New brick should appear in the library immediately (real-time update)

### 4. Brick Display

**In the Library:**
- New brick should appear with:
  - Title: "Elara Moonwhisper"
  - Type badge: "character"
  - Content preview showing the generated prompt text
  - Tags: ["wise mentor", "character"]

**Brick Content:**
The generated prompt text should read:
"The character is Elara Moonwhisper, a Wise Mentor. Backstory: A former court wizard who was exiled for refusing to use dark magic. She now lives in the forest, helping young adventurers discover their true potential. Their primary goal is: To train the next generation of heroes while seeking redemption for her past mistakes."

### 5. Drag and Drop

**Testing Drag Functionality:**
- New character bricks should be draggable
- Drag the new brick to the prompt assembler
- Should appear in the assembled bricks area
- Should update the prompt preview with the character information

### 6. Firebase Integration

**With Firebase Configured:**
- Bricks should save to Firestore
- Check browser console for "Brick saved to Firestore with ID: [id]"
- Real-time updates should work across browser tabs

**Without Firebase (Demo Mode):**
- Bricks should save to local state
- Check browser console for "Firebase not initialized, adding to sample data"
- Bricks persist until page refresh

### 7. Console Logging

**Expected Console Messages:**
```
Firebase initialized successfully (if configured)
User authenticated: [user-id] (if Firebase configured)
Bricks loaded from Firestore: [...] (if Firebase configured)
Current state.bricks: [...]
Current appState.bricks: [...]
Brick saved successfully: {...}
```

## Common Issues and Solutions

### Modal Not Appearing
- Check that Tailwind CSS is loaded
- Verify modal HTML structure is present
- Check browser console for JavaScript errors

### Form Submission Failing
- Verify all required fields are filled
- Check browser console for error messages
- Ensure Firebase configuration is correct (if using Firebase)

### Bricks Not Displaying
- Check console for "Current state.bricks" and "Current appState.bricks" logs
- Verify renderBrickLibrary() is being called
- Check for JavaScript errors in console

### Drag and Drop Not Working
- Ensure brick elements have draggable="true"
- Check that event listeners are properly attached
- Verify drop zone event handlers are working

## Browser Developer Tools

**Console Tab:**
- Monitor for error messages
- Check Firebase initialization status
- View brick creation and loading logs

**Network Tab:**
- Monitor Firebase requests (if configured)
- Check for failed API calls

**Elements Tab:**
- Inspect modal HTML structure
- Verify CSS classes are applied correctly
- Check form field values

## Test Scenarios

### Scenario 1: Basic Functionality
1. Open the app
2. Click "+ New Brick"
3. Fill in all fields with sample data
4. Click "Save Brick"
5. Verify brick appears in library
6. Drag brick to assembler
7. Check prompt preview updates

### Scenario 2: Validation Testing
1. Open modal
2. Try submitting empty form
3. Fill in only some fields
4. Try submitting partial form
5. Fill in all fields and submit successfully

### Scenario 3: Modal Interaction
1. Open modal with "+ New Brick"
2. Close with "Cancel" button
3. Open modal again
4. Close by clicking overlay
5. Open modal again
6. Close with ESC key
7. Verify form is cleared each time

### Scenario 4: Multiple Bricks
1. Create 3-4 different character bricks
2. Verify all appear in library
3. Test dragging different bricks
4. Create a story with multiple characters

## Success Criteria

✅ Modal opens and closes properly
✅ Form validation works correctly
✅ Bricks save successfully (Firebase or demo mode)
✅ New bricks appear in library immediately
✅ Bricks are draggable and functional
✅ Prompt preview updates correctly
✅ Console logging shows expected messages
✅ No JavaScript errors in console

## Troubleshooting

If you encounter issues:
1. Check browser console for errors
2. Verify Firebase configuration (if using Firebase)
3. Ensure all required files are present
4. Test in different browsers
5. Check network connectivity (for Firebase)

## Next Steps

After successful testing, you're ready for Phase 4 which will add:
- Additional brick types (Setting, Plot, etc.)
- Brick editing functionality
- Enhanced UI/UX improvements
- Advanced Firebase features
