/**
 * Generative Story Bible - Main Application JavaScript
 * 
 * This module handles the core functionality of the Story Bible app
 * including brick management, drag & drop, and story generation.
 */

// Application state
let appState = {
    bricks: [],
    assembledBricks: [],
    currentStory: null,
    isGenerating: false
};

// DOM elements
let elements = {};

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeElements();
    initializeEventListeners();
    loadBricks();
    console.log('Generative Story Bible app initialized');
});

/**
 * Initialize DOM element references
 */
function initializeElements() {
    elements = {
        brickLibraryList: document.getElementById('brick-library-list'),
        addNewBrickBtn: document.getElementById('add-new-brick-btn'),
        promptAssemblerSlots: document.getElementById('prompt-assembler-slots'),
        finalPromptPreview: document.getElementById('final-prompt-preview'),
        generateStoryBtn: document.getElementById('generate-story-btn'),
        aiOutputContent: document.getElementById('ai-output-content')
    };
}

/**
 * Initialize event listeners
 */
function initializeEventListeners() {
    // Add new brick button
    elements.addNewBrickBtn.addEventListener('click', showAddBrickModal);
    
    // Generate story button
    elements.generateStoryBtn.addEventListener('click', generateStory);
    
    // Prompt assembler drop zone
    setupDropZone(elements.promptAssemblerSlots);
}

/**
 * Load bricks from storage
 */
async function loadBricks() {
    try {
        const response = await fetch('api/bricks.php');
        if (response.ok) {
            appState.bricks = await response.json();
            renderBrickLibrary();
        } else {
            console.error('Failed to load bricks');
            // Load sample bricks for demo
            loadSampleBricks();
        }
    } catch (error) {
        console.error('Error loading bricks:', error);
        loadSampleBricks();
    }
}

/**
 * Load sample bricks for demonstration
 */
function loadSampleBricks() {
    appState.bricks = [
        {
            id: 'brick-1',
            title: 'Hero Character',
            content: 'A brave knight named Sir Galahad with a mysterious past',
            type: 'character',
            tags: ['hero', 'knight', 'mystery']
        },
        {
            id: 'brick-2',
            title: 'Dark Forest Setting',
            content: 'An ancient forest shrouded in mist where strange creatures dwell',
            type: 'setting',
            tags: ['forest', 'mysterious', 'creatures']
        },
        {
            id: 'brick-3',
            title: 'Quest Plot',
            content: 'A mission to retrieve a stolen magical artifact before it falls into evil hands',
            type: 'plot',
            tags: ['quest', 'artifact', 'magic']
        }
    ];
    renderBrickLibrary();
}

/**
 * Render the brick library
 */
function renderBrickLibrary() {
    elements.brickLibraryList.innerHTML = '';
    
    appState.bricks.forEach(brick => {
        const brickElement = createBrickElement(brick);
        elements.brickLibraryList.appendChild(brickElement);
    });
}

/**
 * Create a brick element
 */
function createBrickElement(brick) {
    const div = document.createElement('div');
    div.className = 'brick-card bg-slate-700 border border-slate-600 rounded-lg p-3 cursor-move';
    div.draggable = true;
    div.dataset.brickId = brick.id;
    
    div.innerHTML = `
        <div class="flex justify-between items-start mb-2">
            <h3 class="font-medium text-slate-100 text-sm">${brick.title}</h3>
            <span class="text-xs px-2 py-1 bg-slate-600 text-slate-300 rounded">${brick.type}</span>
        </div>
        <p class="text-slate-300 text-xs mb-2 line-clamp-2">${brick.content}</p>
        <div class="flex flex-wrap gap-1">
            ${brick.tags.map(tag => `<span class="text-xs px-1 py-0.5 bg-slate-800 text-slate-400 rounded">${tag}</span>`).join('')}
        </div>
    `;
    
    // Add drag event listeners
    div.addEventListener('dragstart', handleDragStart);
    div.addEventListener('dragend', handleDragEnd);
    
    return div;
}

/**
 * Handle drag start
 */
function handleDragStart(e) {
    e.dataTransfer.setData('text/plain', e.target.dataset.brickId);
    e.target.style.opacity = '0.5';
}

/**
 * Handle drag end
 */
function handleDragEnd(e) {
    e.target.style.opacity = '1';
}

/**
 * Setup drop zone
 */
function setupDropZone(element) {
    element.addEventListener('dragover', handleDragOver);
    element.addEventListener('drop', handleDrop);
    element.addEventListener('dragenter', handleDragEnter);
    element.addEventListener('dragleave', handleDragLeave);
}

/**
 * Handle drag over
 */
function handleDragOver(e) {
    e.preventDefault();
}

/**
 * Handle drag enter
 */
function handleDragEnter(e) {
    e.preventDefault();
    e.target.classList.add('drag-over');
}

/**
 * Handle drag leave
 */
function handleDragLeave(e) {
    e.target.classList.remove('drag-over');
}

/**
 * Handle drop
 */
function handleDrop(e) {
    e.preventDefault();
    e.target.classList.remove('drag-over');
    
    const brickId = e.dataTransfer.getData('text/plain');
    const brick = appState.bricks.find(b => b.id === brickId);
    
    if (brick && !appState.assembledBricks.find(b => b.id === brickId)) {
        appState.assembledBricks.push(brick);
        renderAssembledBricks();
        updatePromptPreview();
    }
}

/**
 * Render assembled bricks
 */
function renderAssembledBricks() {
    if (appState.assembledBricks.length === 0) {
        elements.promptAssemblerSlots.innerHTML = '<p class="text-slate-400 text-center">Drag bricks here to build your prompt.</p>';
        return;
    }
    
    elements.promptAssemblerSlots.innerHTML = '';
    
    appState.assembledBricks.forEach((brick, index) => {
        const brickElement = createAssembledBrickElement(brick, index);
        elements.promptAssemblerSlots.appendChild(brickElement);
    });
}

/**
 * Create assembled brick element
 */
function createAssembledBrickElement(brick, index) {
    const div = document.createElement('div');
    div.className = 'bg-slate-800 border border-slate-600 rounded-lg p-3 mb-3';
    
    div.innerHTML = `
        <div class="flex justify-between items-start mb-2">
            <h3 class="font-medium text-slate-100 text-sm">${brick.title}</h3>
            <button class="text-slate-400 hover:text-red-400 text-sm" onclick="removeBrick(${index})">×</button>
        </div>
        <p class="text-slate-300 text-xs">${brick.content}</p>
    `;
    
    return div;
}

/**
 * Remove brick from assembled bricks
 */
window.removeBrick = function(index) {
    appState.assembledBricks.splice(index, 1);
    renderAssembledBricks();
    updatePromptPreview();
};

/**
 * Update prompt preview
 */
function updatePromptPreview() {
    if (appState.assembledBricks.length === 0) {
        elements.finalPromptPreview.value = '';
        return;
    }
    
    const prompt = appState.assembledBricks
        .map(brick => `${brick.title}: ${brick.content}`)
        .join('\n\n');
    
    elements.finalPromptPreview.value = `Create a story incorporating these elements:\n\n${prompt}`;
}

/**
 * Show add brick modal (placeholder)
 */
function showAddBrickModal() {
    // This would open a modal to add new bricks
    // For now, just show an alert
    alert('Add Brick functionality will be implemented in the next phase');
}

/**
 * Generate story using AI
 */
async function generateStory() {
    if (appState.assembledBricks.length === 0) {
        alert('Please add some bricks to your prompt first');
        return;
    }
    
    if (appState.isGenerating) {
        return;
    }
    
    appState.isGenerating = true;
    elements.generateStoryBtn.disabled = true;
    elements.generateStoryBtn.textContent = 'Generating...';
    
    // Show loading state
    elements.aiOutputContent.innerHTML = `
        <div class="border border-slate-600 rounded-lg p-6 h-full flex items-center justify-center">
            <div class="text-center">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
                <p class="text-slate-400">Generating your story...</p>
            </div>
        </div>
    `;
    
    try {
        const response = await fetch('api/ai-generate.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                prompt: elements.finalPromptPreview.value,
                bricks: appState.assembledBricks
            })
        });
        
        if (response.ok) {
            const result = await response.json();
            displayGeneratedStory(result.story);
        } else {
            throw new Error('Failed to generate story');
        }
    } catch (error) {
        console.error('Error generating story:', error);
        displayError('Failed to generate story. Please try again.');
    } finally {
        appState.isGenerating = false;
        elements.generateStoryBtn.disabled = false;
        elements.generateStoryBtn.textContent = 'Generate Story';
    }
}

/**
 * Display generated story
 */
function displayGeneratedStory(story) {
    elements.aiOutputContent.innerHTML = `
        <div class="border border-slate-600 rounded-lg p-6 h-full">
            <div class="prose prose-invert max-w-none">
                <div class="whitespace-pre-wrap text-slate-200">${story}</div>
            </div>
        </div>
    `;
}

/**
 * Display error message
 */
function displayError(message) {
    elements.aiOutputContent.innerHTML = `
        <div class="border border-red-600 rounded-lg p-6 h-full flex items-center justify-center">
            <div class="text-center">
                <p class="text-red-400">${message}</p>
            </div>
        </div>
    `;
}

// Export for use in other modules
export { appState, loadBricks, renderBrickLibrary };
