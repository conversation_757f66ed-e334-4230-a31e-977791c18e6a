/**
 * Generative Story Bible - Main Application JavaScript
 *
 * This module handles the core functionality of the Story Bible app
 * including brick management, drag & drop, and story generation.
 */

// Firebase imports
import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
import {
    getFirestore,
    collection,
    doc,
    addDoc,
    getDocs,
    onSnapshot,
    query
} from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';
import {
    getAuth,
    signInAnonymously,
    onAuthStateChanged
} from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';

// Import Firebase configuration
import { firebaseConfig, appId } from './firebase-config.js';

// Initialize Firebase with error handling
let app, db, auth;
let firebaseInitialized = false;

try {
    // Check if Firebase config is properly set
    if (firebaseConfig.apiKey === "your-api-key-here" || !firebaseConfig.apiKey) {
        console.warn('Firebase not configured. Using sample data mode.');
        firebaseInitialized = false;
    } else {
        app = initializeApp(firebaseConfig);
        db = getFirestore(app);
        auth = getAuth(app);
        firebaseInitialized = true;
        console.log('Firebase initialized successfully');
    }
} catch (error) {
    console.error('Firebase initialization failed:', error);
    firebaseInitialized = false;
}

// Global state
const state = {
    bricks: [],
    assembler: {
        Character: null,
        Setting: null
    }
};

// Application state (keeping for backward compatibility)
let appState = {
    bricks: [],
    assembledBricks: [],
    currentStory: null,
    isGenerating: false,
    userId: null
};

// DOM elements
let elements = {};

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeElements();
    initializeEventListeners();
    initializeAuth();
    console.log('Generative Story Bible app initialized');
});

/**
 * Initialize Firebase authentication
 */
function initializeAuth() {
    if (!firebaseInitialized) {
        console.log('Firebase not initialized, using sample data');
        appState.userId = 'demo-user';
        loadSampleBricks();
        return;
    }

    onAuthStateChanged(auth, (user) => {
        if (user) {
            // User is signed in
            appState.userId = user.uid;
            console.log('User authenticated:', user.uid);
            getBricks();
        } else {
            // No user is signed in, sign in anonymously
            signInAnonymously(auth)
                .then(() => {
                    console.log('Anonymous sign-in successful');
                })
                .catch((error) => {
                    console.error('Anonymous sign-in failed:', error);
                    // Fallback to sample bricks if Firebase fails
                    loadSampleBricks();
                });
        }
    });
}

/**
 * Get bricks from Firestore
 */
async function getBricks() {
    if (!firebaseInitialized) {
        console.log('Firebase not initialized, using sample data');
        loadSampleBricks();
        return;
    }

    if (!appState.userId) {
        console.error('No user ID available');
        loadSampleBricks();
        return;
    }

    try {
        const bricksPath = `artifacts/${appId}/users/${appState.userId}/bricks`;
        const bricksCollection = collection(db, bricksPath);
        const bricksQuery = query(bricksCollection);

        // Set up real-time listener
        onSnapshot(bricksQuery, (querySnapshot) => {
            const bricks = [];
            querySnapshot.forEach((doc) => {
                bricks.push({
                    id: doc.id,
                    ...doc.data()
                });
            });

            // Update both state objects
            state.bricks = bricks;
            appState.bricks = bricks;

            console.log('Bricks loaded from Firestore:', bricks);
            renderBrickLibrary();
        }, (error) => {
            console.error('Error listening to bricks:', error);
            loadSampleBricks();
        });

    } catch (error) {
        console.error('Error setting up bricks listener:', error);
        loadSampleBricks();
    }
}

/**
 * Initialize DOM element references
 */
function initializeElements() {
    elements = {
        brickLibraryList: document.getElementById('brick-library-list'),
        addNewBrickBtn: document.getElementById('add-new-brick-btn'),
        promptAssemblerSlots: document.getElementById('prompt-assembler-slots'),
        finalPromptPreview: document.getElementById('final-prompt-preview'),
        generateStoryBtn: document.getElementById('generate-story-btn'),
        aiOutputContent: document.getElementById('ai-output-content'),
        // Modal elements
        modalOverlay: document.getElementById('modal-overlay'),
        brickModal: document.getElementById('brick-modal'),
        brickForm: document.getElementById('brick-form'),
        cancelBrickBtn: document.getElementById('cancel-brick-btn'),
        // Form inputs
        brickNameInput: document.getElementById('brick-name-input'),
        characterArchetypeInput: document.getElementById('character-archetype-input'),
        characterBackstoryInput: document.getElementById('character-backstory-input'),
        characterGoalInput: document.getElementById('character-goal-input'),
        // Drop zone elements
        characterSlot: document.getElementById('character-slot'),
        settingSlot: document.getElementById('setting-slot')
    };
}

/**
 * Initialize event listeners
 */
function initializeEventListeners() {
    // Add new brick button
    elements.addNewBrickBtn.addEventListener('click', showAddBrickModal);

    // Generate story button
    elements.generateStoryBtn.addEventListener('click', generateStory);

    // Modal event listeners
    elements.cancelBrickBtn.addEventListener('click', hideAddBrickModal);
    elements.modalOverlay.addEventListener('click', handleModalOverlayClick);
    elements.brickForm.addEventListener('submit', handleBrickFormSubmit);

    // Keyboard event listeners
    document.addEventListener('keydown', handleKeyDown);

    // Drag and Drop Setup (Part 1): Add dragstart event listener using event delegation
    elements.brickLibraryList.addEventListener('dragstart', handleBrickDragStart);
    elements.brickLibraryList.addEventListener('dragend', handleBrickDragEnd);

    // Drag and Drop Logic (Part 2): Prompt assembler drop zones
    elements.promptAssemblerSlots.addEventListener('dragover', handleAssemblerDragOver);
    elements.promptAssemblerSlots.addEventListener('drop', handleAssemblerDrop);
    elements.promptAssemblerSlots.addEventListener('dragenter', handleAssemblerDragEnter);
    elements.promptAssemblerSlots.addEventListener('dragleave', handleAssemblerDragLeave);
}

/**
 * Load bricks from storage (legacy function - now using Firebase)
 */
async function loadBricks() {
    // This function is now handled by getBricks() and Firebase auth
    // Keeping for backward compatibility
    console.log('loadBricks() called - now handled by Firebase auth flow');
}

/**
 * Load sample bricks for demonstration
 */
function loadSampleBricks() {
    const sampleBricks = [
        {
            id: 'brick-1',
            title: 'Hero Character',
            content: 'A brave knight named Sir Galahad with a mysterious past',
            type: 'character',
            tags: ['hero', 'knight', 'mystery']
        },
        {
            id: 'brick-2',
            title: 'Dark Forest Setting',
            content: 'An ancient forest shrouded in mist where strange creatures dwell',
            type: 'setting',
            tags: ['forest', 'mysterious', 'creatures']
        },
        {
            id: 'brick-3',
            title: 'Quest Plot',
            content: 'A mission to retrieve a stolen magical artifact before it falls into evil hands',
            type: 'plot',
            tags: ['quest', 'artifact', 'magic']
        }
    ];

    // Update both state objects
    state.bricks = sampleBricks;
    appState.bricks = sampleBricks;

    console.log('Sample bricks loaded');
    renderBrickLibrary();
}

/**
 * Render the brick library
 */
function renderBrickLibrary() {
    // Clear the brick library list
    elements.brickLibraryList.innerHTML = '';

    // Log the contents of state.bricks to console
    console.log('Current state.bricks:', state.bricks);
    console.log('Current appState.bricks:', appState.bricks);

    // Iterate over the state.bricks array
    state.bricks.forEach(brick => {
        // Create a div element for the card
        const cardDiv = document.createElement('div');

        // Card Styling: Use Tailwind CSS for background, padding, rounded corners, and margin
        cardDiv.className = 'brick-card bg-slate-700 border border-slate-600 rounded-lg p-3 mb-3 cursor-move transition-all duration-200 hover:bg-slate-600';

        // Data Attributes: Add data-brick-id attribute with Firestore document ID
        cardDiv.setAttribute('data-brick-id', brick.id);

        // Draggable: Add draggable="true" attribute
        cardDiv.setAttribute('draggable', 'true');

        // Handle both new and legacy brick formats for display
        const brickName = brick.brickName || brick.title || 'Untitled Brick';
        const brickType = brick.brickType || brick.type || 'unknown';

        // Card Content: Add h3 for brick.brickName and p for brick.brickType
        cardDiv.innerHTML = `
            <h3 class="font-medium text-slate-100 text-sm mb-1">${escapeHtml(brickName)}</h3>
            <p class="text-xs text-slate-400">${escapeHtml(brickType)}</p>
        `;

        // Append the completed card to the brick library list container
        elements.brickLibraryList.appendChild(cardDiv);
    });
}



/**
 * Escape HTML to prevent XSS
 */
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * Handle brick drag start using event delegation
 */
function handleBrickDragStart(e) {
    // Check if the dragged element has draggable="true"
    if (e.target.getAttribute('draggable') === 'true') {
        // Find the closest parent card element
        const cardElement = e.target.closest('[data-brick-id]');

        if (cardElement) {
            // Get the brick ID from the data attribute
            const brickId = cardElement.getAttribute('data-brick-id');

            // Store the brick ID in the data transfer
            e.dataTransfer.setData('text/plain', brickId);

            // Visual feedback during drag
            cardElement.style.opacity = '0.5';
            cardElement.classList.add('dragging');
        }
    }
}

/**
 * Handle brick drag end using event delegation
 */
function handleBrickDragEnd(e) {
    // Check if the dragged element has draggable="true"
    if (e.target.getAttribute('draggable') === 'true') {
        // Find the closest parent card element
        const cardElement = e.target.closest('[data-brick-id]');

        if (cardElement) {
            // Restore visual state
            cardElement.style.opacity = '1';
            cardElement.classList.remove('dragging');
        }
    }
}

/**
 * Handle drag over for assembler slots
 */
function handleAssemblerDragOver(e) {
    e.preventDefault(); // Allow dropping
}

/**
 * Handle drag enter for assembler slots
 */
function handleAssemblerDragEnter(e) {
    e.preventDefault();
    const targetSlot = e.target.closest('[data-slot-type]');
    if (targetSlot) {
        targetSlot.classList.add('drag-over');
    }
}

/**
 * Handle drag leave for assembler slots
 */
function handleAssemblerDragLeave(e) {
    const targetSlot = e.target.closest('[data-slot-type]');
    if (targetSlot && !targetSlot.contains(e.relatedTarget)) {
        targetSlot.classList.remove('drag-over');
    }
}

/**
 * Handle drop for assembler slots
 */
function handleAssemblerDrop(e) {
    e.preventDefault();

    // Check if the drop target is a slot or find the closest slot
    let targetSlot = e.target.closest('[data-slot-type]');

    // Remove drag-over class
    if (targetSlot) {
        targetSlot.classList.remove('drag-over');
    }

    // Get the brick ID from the data transfer
    const brickId = e.dataTransfer.getData('text/plain');

    // Find the brick object from state.bricks
    const brick = state.bricks.find(b => b.id === brickId);

    if (!brick) {
        console.log('Brick not found:', brickId);
        return;
    }

    if (!targetSlot) {
        console.log('Not dropped on a valid slot');
        return;
    }

    // Get the slot type from the target
    const slotType = targetSlot.getAttribute('data-slot-type');

    // Validation: Check if brick type matches slot type
    const brickType = brick.brickType || brick.type || 'unknown';

    if (brickType.toLowerCase() === slotType.toLowerCase()) {
        // Update the state
        state.assembler[slotType] = brickId;
        console.log(`Placed ${brickType} brick in ${slotType} slot:`, brick);

        // Render the assembler
        renderAssembler();
    } else {
        console.log(`Type mismatch: Cannot place ${brickType} brick in ${slotType} slot`);
    }
}

/**
 * Render the assembler based on state.assembler
 */
function renderAssembler() {
    // Define slot types and their corresponding elements
    const slotTypes = ['Character', 'Setting'];

    slotTypes.forEach(slotType => {
        // Find the corresponding slot div
        const slotElement = document.getElementById(`${slotType.toLowerCase()}-slot`);

        if (!slotElement) {
            console.error(`Slot element not found: ${slotType.toLowerCase()}-slot`);
            return;
        }

        // Check if there's a brick in this slot
        const brickId = state.assembler[slotType];

        if (brickId) {
            // Find the corresponding brick data
            const brick = state.bricks.find(b => b.id === brickId);

            if (brick) {
                // Clear the slot's content and replace with mini-card
                const brickName = brick.brickName || brick.title || 'Untitled Brick';
                const brickType = brick.brickType || brick.type || 'unknown';

                slotElement.innerHTML = `
                    <div class="bg-slate-700 border border-slate-500 rounded-lg p-3">
                        <div class="flex justify-between items-start mb-2">
                            <h4 class="font-medium text-slate-100 text-sm">${escapeHtml(brickName)}</h4>
                            <button class="text-slate-400 hover:text-red-400 text-sm" onclick="removeFromSlot('${slotType}')">×</button>
                        </div>
                        <p class="text-xs text-slate-400">${escapeHtml(brickType)}</p>
                    </div>
                `;

                // Remove dashed border and add solid border
                slotElement.className = 'border-2 border-solid border-slate-500 rounded-lg p-6 text-center transition-all duration-200';
            } else {
                console.error(`Brick not found for ID: ${brickId}`);
                // Reset to default state if brick not found
                state.assembler[slotType] = null;
                resetSlotToDefault(slotElement, slotType);
            }
        } else {
            // Reset to default "Drop brick here" text
            resetSlotToDefault(slotElement, slotType);
        }
    });

    // Update prompt preview after rendering assembler
    updateAssemblerPromptPreview();
}

/**
 * Reset slot to default state
 */
function resetSlotToDefault(slotElement, slotType) {
    slotElement.innerHTML = `<p class="text-slate-400">Drop ${slotType} Brick Here</p>`;
    slotElement.className = 'border-2 border-dashed border-slate-600 rounded-lg p-6 text-center transition-all duration-200 hover:border-slate-500 hover:bg-slate-800/50';
    slotElement.setAttribute('data-slot-type', slotType);
}

/**
 * Remove brick from slot
 */
window.removeFromSlot = function(slotType) {
    state.assembler[slotType] = null;
    renderAssembler();
};

/**
 * Update prompt preview based on assembler state
 */
function updateAssemblerPromptPreview() {
    const promptParts = [];

    // Build prompt string by combining promptText of bricks in state.assembler
    Object.keys(state.assembler).forEach(slotType => {
        const brickId = state.assembler[slotType];

        if (brickId) {
            const brick = state.bricks.find(b => b.id === brickId);

            if (brick) {
                const promptText = brick.promptText || brick.content || `${brick.brickName || brick.title}: No content available`;
                promptParts.push(promptText);
            }
        }
    });

    // Set the value of the final prompt preview textarea
    if (promptParts.length > 0) {
        const combinedPrompt = `Create a story incorporating these elements:\n\n${promptParts.join('\n\n')}`;
        elements.finalPromptPreview.value = combinedPrompt;
    } else {
        elements.finalPromptPreview.value = '';
    }
}



/**
 * Show add brick modal
 */
function showAddBrickModal() {
    elements.modalOverlay.classList.remove('hidden');
    elements.brickNameInput.focus();
}

/**
 * Hide add brick modal
 */
function hideAddBrickModal() {
    elements.modalOverlay.classList.add('hidden');
    clearBrickForm();
}

/**
 * Handle modal overlay click (close modal when clicking outside)
 */
function handleModalOverlayClick(e) {
    if (e.target === elements.modalOverlay) {
        hideAddBrickModal();
    }
}

/**
 * Handle keyboard events
 */
function handleKeyDown(e) {
    // Close modal with ESC key
    if (e.key === 'Escape' && !elements.modalOverlay.classList.contains('hidden')) {
        hideAddBrickModal();
    }
}

/**
 * Clear brick form fields
 */
function clearBrickForm() {
    elements.brickNameInput.value = '';
    elements.characterArchetypeInput.value = '';
    elements.characterBackstoryInput.value = '';
    elements.characterGoalInput.value = '';
}

/**
 * Handle brick form submission
 */
async function handleBrickFormSubmit(e) {
    e.preventDefault();

    // Get form values
    const brickName = elements.brickNameInput.value.trim();
    const archetype = elements.characterArchetypeInput.value.trim();
    const backstory = elements.characterBackstoryInput.value.trim();
    const goal = elements.characterGoalInput.value.trim();

    // Validate required fields
    if (!brickName || !archetype || !backstory || !goal) {
        alert('Please fill in all fields');
        return;
    }

    // Show loading state
    const submitBtn = elements.brickForm.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.disabled = true;
    submitBtn.textContent = 'Saving...';

    // Create new brick object
    const newBrick = {
        brickType: 'Character',
        brickName: brickName,
        data: {
            archetype: archetype,
            backstory: backstory,
            goal: goal
        },
        promptText: `The character is ${brickName}, a ${archetype}. Backstory: ${backstory}. Their primary goal is: ${goal}.`,
        // Additional metadata for compatibility with existing system
        title: brickName,
        content: `The character is ${brickName}, a ${archetype}. Backstory: ${backstory}. Their primary goal is: ${goal}.`,
        type: 'character',
        tags: [archetype.toLowerCase(), 'character'],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
    };

    try {
        await saveBrick(newBrick);
        hideAddBrickModal();
        console.log('Brick saved successfully:', newBrick);
    } catch (error) {
        console.error('Error saving brick:', error);
        alert('Failed to save brick. Please try again.');
    } finally {
        // Restore button state
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;
    }
}

/**
 * Save brick to Firestore
 */
async function saveBrick(brickData) {
    if (!firebaseInitialized) {
        console.log('Firebase not initialized, adding to sample data');
        // Add to local state for demo mode
        const brickWithId = {
            id: 'brick-' + Date.now(),
            ...brickData
        };
        state.bricks.push(brickWithId);
        appState.bricks.push(brickWithId);
        renderBrickLibrary();
        return brickWithId;
    }

    if (!appState.userId) {
        throw new Error('No user ID available');
    }

    try {
        const bricksPath = `artifacts/${appId}/users/${appState.userId}/bricks`;
        const bricksCollection = collection(db, bricksPath);

        const docRef = await addDoc(bricksCollection, brickData);
        console.log('Brick saved to Firestore with ID:', docRef.id);

        return {
            id: docRef.id,
            ...brickData
        };
    } catch (error) {
        console.error('Error saving brick to Firestore:', error);
        throw error;
    }
}

/**
 * Generate a demo story when API key is not available
 */
function generateDemoStory(prompt) {
    const demoStories = [
        "In a land where magic still flows through ancient stones, our hero embarks on a journey that will test their courage and resolve. Through dark forests and across treacherous mountains, they must face challenges that will either forge them into a legend or break them entirely.\n\nThe path ahead is uncertain, but with determination in their heart and hope as their guide, they take the first step into an adventure that will change everything.",

        "Once upon a time, in a realm where dreams and reality intertwine, a brave soul set forth on a quest of great importance. The world around them was filled with wonder and danger in equal measure, where every shadow could hide a friend or foe.\n\nAs they journeyed through this mystical landscape, they discovered that the greatest adventures often begin with a single act of courage, and that heroes are not born, but forged through the trials they choose to face.",

        "The story begins in a place where legends are born and destinies are written. Our protagonist, armed with nothing but their wits and an unshakeable belief in what is right, must navigate a world full of mysteries and challenges.\n\nEach step forward reveals new truths about themselves and the world around them, proving that sometimes the most extraordinary adventures start with the most ordinary people who choose to do extraordinary things."
    ];

    // Select a random demo story
    const randomIndex = Math.floor(Math.random() * demoStories.length);
    let selectedStory = demoStories[randomIndex];

    // Add a note about demo mode
    selectedStory += "\n\n---\n\n*This is a demo story generated without AI. To get personalized stories based on your bricks, please configure your Gemini API key. See the setup guide for instructions.*";

    return selectedStory;
}

/**
 * Get Gemini API key from secure environment
 * In a production environment, this should be handled by the dashboard's secure configuration
 */
function getGeminiApiKey() {
    // Try to get API key from dashboard configuration
    if (window.dashboardConfig && window.dashboardConfig.geminiApiKey) {
        return window.dashboardConfig.geminiApiKey;
    }

    // Try to get from environment variable (if available)
    if (typeof process !== 'undefined' && process.env && process.env.GEMINI_API_KEY) {
        return process.env.GEMINI_API_KEY;
    }

    // For development/testing, you can temporarily set it here
    // WARNING: Never commit real API keys to version control!
    const devApiKey = ""; // Set your API key here for testing

    if (!devApiKey) {
        throw new Error('Gemini API key not configured. Please set up your API key in the dashboard configuration.');
    }

    return devApiKey;
}

/**
 * Generate story using AI
 */
async function generateStory() {
    // Get the final prompt text from the textarea
    const finalPrompt = elements.finalPromptPreview.value.trim();

    // If the prompt is empty, do nothing
    if (!finalPrompt) {
        alert('Please add some bricks to your prompt first');
        return;
    }

    if (appState.isGenerating) {
        return;
    }

    appState.isGenerating = true;
    elements.generateStoryBtn.disabled = true;
    elements.generateStoryBtn.textContent = 'Generating...';

    // Show loading indicator in the ai-output-content div
    elements.aiOutputContent.innerHTML = `
        <div class="border border-slate-600 rounded-lg p-6 h-full flex items-center justify-center">
            <div class="text-center">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
                <p class="text-slate-400">Generating your story...</p>
            </div>
        </div>
    `;

    try {
        // Try to get API key, fall back to demo mode if not available
        let apiKey;
        try {
            apiKey = getGeminiApiKey();
        } catch (keyError) {
            // If no API key is available, use demo mode
            console.log('API key not available, using demo mode');
            const demoStory = generateDemoStory(finalPrompt);
            displayGeneratedStory(demoStory);
            return;
        }

        // API Call Structure for Gemini API
        const payload = {
            contents: [{
                role: "user",
                parts: [{ text: finalPrompt }]
            }]
        };

        const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;

        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(payload)
        });

        if (!response.ok) {
            throw new Error(`API request failed with status ${response.status}`);
        }

        const result = await response.json();

        // Handle Response: Check for valid response
        if (result.candidates &&
            result.candidates[0] &&
            result.candidates[0].content &&
            result.candidates[0].content.parts &&
            result.candidates[0].content.parts[0] &&
            result.candidates[0].content.parts[0].text) {

            const generatedText = result.candidates[0].content.parts[0].text;
            displayGeneratedStory(generatedText);
        } else {
            throw new Error('Invalid response format from Gemini API');
        }

    } catch (error) {
        console.error('Error generating story:', error);

        // Provide more specific error messages based on error type
        let errorMessage = 'Failed to generate story. Please try again.';

        if (error.message.includes('API key not configured')) {
            errorMessage = 'Gemini API key not configured. Please set up your API key to use AI generation.';
        } else if (error.message.includes('status 401')) {
            errorMessage = 'Invalid API key. Please check your Gemini API key configuration.';
        } else if (error.message.includes('status 403')) {
            errorMessage = 'API access denied. Please check your billing and quota settings.';
        } else if (error.message.includes('status 429')) {
            errorMessage = 'API rate limit exceeded. Please wait a moment and try again.';
        } else if (error.message.includes('Failed to fetch')) {
            errorMessage = 'Network error. Please check your internet connection and try again.';
        }

        displayError(errorMessage);
    } finally {
        appState.isGenerating = false;
        elements.generateStoryBtn.disabled = false;
        elements.generateStoryBtn.textContent = 'Generate Story';
    }
}

/**
 * Display generated story
 */
function displayGeneratedStory(story) {
    // Escape HTML to prevent XSS attacks
    const escapedStory = escapeHtml(story);

    // Replace newlines with <br> tags for proper formatting
    const formattedStory = escapedStory.replace(/\n/g, '<br>');

    elements.aiOutputContent.innerHTML = `
        <div class="border border-slate-600 rounded-lg p-6 h-full">
            <div class="prose prose-invert max-w-none">
                <div class="text-slate-200 leading-relaxed">${formattedStory}</div>
            </div>
        </div>
    `;
}

/**
 * Display error message
 */
function displayError(message) {
    elements.aiOutputContent.innerHTML = `
        <div class="border border-red-600 rounded-lg p-6 h-full flex items-center justify-center">
            <div class="text-center">
                <p class="text-red-400">${message}</p>
            </div>
        </div>
    `;
}

// Export for use in other modules
export { appState, loadBricks, renderBrickLibrary };
