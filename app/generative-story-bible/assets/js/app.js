/**
 * Generative Story Bible - Main Application JavaScript
 *
 * This module handles the core functionality of the Story Bible app
 * including brick management, drag & drop, and story generation.
 */

// Firebase imports
import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
import {
    getFirestore,
    collection,
    doc,
    addDoc,
    getDocs,
    onSnapshot,
    query
} from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';
import {
    getAuth,
    signInAnonymously,
    onAuthStateChanged
} from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';

// Import Firebase configuration
import { firebaseConfig, appId } from './firebase-config.js';

// Initialize Firebase with error handling
let app, db, auth;
let firebaseInitialized = false;

try {
    // Check if Firebase config is properly set
    if (firebaseConfig.apiKey === "your-api-key-here" || !firebaseConfig.apiKey) {
        console.warn('Firebase not configured. Using sample data mode.');
        firebaseInitialized = false;
    } else {
        app = initializeApp(firebaseConfig);
        db = getFirestore(app);
        auth = getAuth(app);
        firebaseInitialized = true;
        console.log('Firebase initialized successfully');
    }
} catch (error) {
    console.error('Firebase initialization failed:', error);
    firebaseInitialized = false;
}

// Global state
const state = {
    bricks: []
};

// Application state (keeping for backward compatibility)
let appState = {
    bricks: [],
    assembledBricks: [],
    currentStory: null,
    isGenerating: false,
    userId: null
};

// DOM elements
let elements = {};

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeElements();
    initializeEventListeners();
    initializeAuth();
    console.log('Generative Story Bible app initialized');
});

/**
 * Initialize Firebase authentication
 */
function initializeAuth() {
    if (!firebaseInitialized) {
        console.log('Firebase not initialized, using sample data');
        appState.userId = 'demo-user';
        loadSampleBricks();
        return;
    }

    onAuthStateChanged(auth, (user) => {
        if (user) {
            // User is signed in
            appState.userId = user.uid;
            console.log('User authenticated:', user.uid);
            getBricks();
        } else {
            // No user is signed in, sign in anonymously
            signInAnonymously(auth)
                .then(() => {
                    console.log('Anonymous sign-in successful');
                })
                .catch((error) => {
                    console.error('Anonymous sign-in failed:', error);
                    // Fallback to sample bricks if Firebase fails
                    loadSampleBricks();
                });
        }
    });
}

/**
 * Get bricks from Firestore
 */
async function getBricks() {
    if (!firebaseInitialized) {
        console.log('Firebase not initialized, using sample data');
        loadSampleBricks();
        return;
    }

    if (!appState.userId) {
        console.error('No user ID available');
        loadSampleBricks();
        return;
    }

    try {
        const bricksPath = `artifacts/${appId}/users/${appState.userId}/bricks`;
        const bricksCollection = collection(db, bricksPath);
        const bricksQuery = query(bricksCollection);

        // Set up real-time listener
        onSnapshot(bricksQuery, (querySnapshot) => {
            const bricks = [];
            querySnapshot.forEach((doc) => {
                bricks.push({
                    id: doc.id,
                    ...doc.data()
                });
            });

            // Update both state objects
            state.bricks = bricks;
            appState.bricks = bricks;

            console.log('Bricks loaded from Firestore:', bricks);
            renderBrickLibrary();
        }, (error) => {
            console.error('Error listening to bricks:', error);
            loadSampleBricks();
        });

    } catch (error) {
        console.error('Error setting up bricks listener:', error);
        loadSampleBricks();
    }
}

/**
 * Initialize DOM element references
 */
function initializeElements() {
    elements = {
        brickLibraryList: document.getElementById('brick-library-list'),
        addNewBrickBtn: document.getElementById('add-new-brick-btn'),
        promptAssemblerSlots: document.getElementById('prompt-assembler-slots'),
        finalPromptPreview: document.getElementById('final-prompt-preview'),
        generateStoryBtn: document.getElementById('generate-story-btn'),
        aiOutputContent: document.getElementById('ai-output-content'),
        // Modal elements
        modalOverlay: document.getElementById('modal-overlay'),
        brickModal: document.getElementById('brick-modal'),
        brickForm: document.getElementById('brick-form'),
        cancelBrickBtn: document.getElementById('cancel-brick-btn'),
        // Form inputs
        brickNameInput: document.getElementById('brick-name-input'),
        characterArchetypeInput: document.getElementById('character-archetype-input'),
        characterBackstoryInput: document.getElementById('character-backstory-input'),
        characterGoalInput: document.getElementById('character-goal-input')
    };
}

/**
 * Initialize event listeners
 */
function initializeEventListeners() {
    // Add new brick button
    elements.addNewBrickBtn.addEventListener('click', showAddBrickModal);

    // Generate story button
    elements.generateStoryBtn.addEventListener('click', generateStory);

    // Prompt assembler drop zone
    setupDropZone(elements.promptAssemblerSlots);

    // Modal event listeners
    elements.cancelBrickBtn.addEventListener('click', hideAddBrickModal);
    elements.modalOverlay.addEventListener('click', handleModalOverlayClick);
    elements.brickForm.addEventListener('submit', handleBrickFormSubmit);

    // Keyboard event listeners
    document.addEventListener('keydown', handleKeyDown);

    // Drag and Drop Setup (Part 1): Add dragstart event listener using event delegation
    elements.brickLibraryList.addEventListener('dragstart', handleBrickDragStart);
    elements.brickLibraryList.addEventListener('dragend', handleBrickDragEnd);
}

/**
 * Load bricks from storage (legacy function - now using Firebase)
 */
async function loadBricks() {
    // This function is now handled by getBricks() and Firebase auth
    // Keeping for backward compatibility
    console.log('loadBricks() called - now handled by Firebase auth flow');
}

/**
 * Load sample bricks for demonstration
 */
function loadSampleBricks() {
    const sampleBricks = [
        {
            id: 'brick-1',
            title: 'Hero Character',
            content: 'A brave knight named Sir Galahad with a mysterious past',
            type: 'character',
            tags: ['hero', 'knight', 'mystery']
        },
        {
            id: 'brick-2',
            title: 'Dark Forest Setting',
            content: 'An ancient forest shrouded in mist where strange creatures dwell',
            type: 'setting',
            tags: ['forest', 'mysterious', 'creatures']
        },
        {
            id: 'brick-3',
            title: 'Quest Plot',
            content: 'A mission to retrieve a stolen magical artifact before it falls into evil hands',
            type: 'plot',
            tags: ['quest', 'artifact', 'magic']
        }
    ];

    // Update both state objects
    state.bricks = sampleBricks;
    appState.bricks = sampleBricks;

    console.log('Sample bricks loaded');
    renderBrickLibrary();
}

/**
 * Render the brick library
 */
function renderBrickLibrary() {
    // Clear the brick library list
    elements.brickLibraryList.innerHTML = '';

    // Log the contents of state.bricks to console
    console.log('Current state.bricks:', state.bricks);
    console.log('Current appState.bricks:', appState.bricks);

    // Iterate over the state.bricks array
    state.bricks.forEach(brick => {
        // Create a div element for the card
        const cardDiv = document.createElement('div');

        // Card Styling: Use Tailwind CSS for background, padding, rounded corners, and margin
        cardDiv.className = 'brick-card bg-slate-700 border border-slate-600 rounded-lg p-3 mb-3 cursor-move transition-all duration-200 hover:bg-slate-600';

        // Data Attributes: Add data-brick-id attribute with Firestore document ID
        cardDiv.setAttribute('data-brick-id', brick.id);

        // Draggable: Add draggable="true" attribute
        cardDiv.setAttribute('draggable', 'true');

        // Handle both new and legacy brick formats for display
        const brickName = brick.brickName || brick.title || 'Untitled Brick';
        const brickType = brick.brickType || brick.type || 'unknown';

        // Card Content: Add h3 for brick.brickName and p for brick.brickType
        cardDiv.innerHTML = `
            <h3 class="font-medium text-slate-100 text-sm mb-1">${escapeHtml(brickName)}</h3>
            <p class="text-xs text-slate-400">${escapeHtml(brickType)}</p>
        `;

        // Append the completed card to the brick library list container
        elements.brickLibraryList.appendChild(cardDiv);
    });
}



/**
 * Escape HTML to prevent XSS
 */
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * Handle brick drag start using event delegation
 */
function handleBrickDragStart(e) {
    // Check if the dragged element has draggable="true"
    if (e.target.getAttribute('draggable') === 'true') {
        // Find the closest parent card element
        const cardElement = e.target.closest('[data-brick-id]');

        if (cardElement) {
            // Get the brick ID from the data attribute
            const brickId = cardElement.getAttribute('data-brick-id');

            // Store the brick ID in the data transfer
            e.dataTransfer.setData('text/plain', brickId);

            // Visual feedback during drag
            cardElement.style.opacity = '0.5';
            cardElement.classList.add('dragging');
        }
    }
}

/**
 * Handle brick drag end using event delegation
 */
function handleBrickDragEnd(e) {
    // Check if the dragged element has draggable="true"
    if (e.target.getAttribute('draggable') === 'true') {
        // Find the closest parent card element
        const cardElement = e.target.closest('[data-brick-id]');

        if (cardElement) {
            // Restore visual state
            cardElement.style.opacity = '1';
            cardElement.classList.remove('dragging');
        }
    }
}

/**
 * Setup drop zone
 */
function setupDropZone(element) {
    element.addEventListener('dragover', handleDragOver);
    element.addEventListener('drop', handleDrop);
    element.addEventListener('dragenter', handleDragEnter);
    element.addEventListener('dragleave', handleDragLeave);
}

/**
 * Handle drag over
 */
function handleDragOver(e) {
    e.preventDefault();
}

/**
 * Handle drag enter
 */
function handleDragEnter(e) {
    e.preventDefault();
    e.target.classList.add('drag-over');
}

/**
 * Handle drag leave
 */
function handleDragLeave(e) {
    e.target.classList.remove('drag-over');
}

/**
 * Handle drop
 */
function handleDrop(e) {
    e.preventDefault();
    e.target.classList.remove('drag-over');

    const brickId = e.dataTransfer.getData('text/plain');
    // Search in both state.bricks and appState.bricks for compatibility
    const brick = state.bricks.find(b => b.id === brickId) || appState.bricks.find(b => b.id === brickId);

    if (brick && !appState.assembledBricks.find(b => b.id === brickId)) {
        appState.assembledBricks.push(brick);
        renderAssembledBricks();
        updatePromptPreview();
    }
}

/**
 * Render assembled bricks
 */
function renderAssembledBricks() {
    if (appState.assembledBricks.length === 0) {
        elements.promptAssemblerSlots.innerHTML = '<p class="text-slate-400 text-center">Drag bricks here to build your prompt.</p>';
        return;
    }
    
    elements.promptAssemblerSlots.innerHTML = '';
    
    appState.assembledBricks.forEach((brick, index) => {
        const brickElement = createAssembledBrickElement(brick, index);
        elements.promptAssemblerSlots.appendChild(brickElement);
    });
}

/**
 * Create assembled brick element
 */
function createAssembledBrickElement(brick, index) {
    const div = document.createElement('div');
    div.className = 'assembled-brick bg-slate-800 border border-slate-600 rounded-lg p-3 mb-3';

    // Handle both new and legacy brick formats
    const title = brick.title || brick.brickName || 'Untitled Brick';
    const content = brick.content || brick.promptText || 'No content';

    div.innerHTML = `
        <div class="flex justify-between items-start mb-2">
            <h3 class="font-medium text-slate-100 text-sm">${escapeHtml(title)}</h3>
            <button class="text-slate-400 hover:text-red-400 text-sm" onclick="removeBrick(${index})">×</button>
        </div>
        <p class="text-slate-300 text-xs">${escapeHtml(content)}</p>
    `;

    return div;
}

/**
 * Remove brick from assembled bricks
 */
window.removeBrick = function(index) {
    appState.assembledBricks.splice(index, 1);
    renderAssembledBricks();
    updatePromptPreview();
};

/**
 * Update prompt preview
 */
function updatePromptPreview() {
    if (appState.assembledBricks.length === 0) {
        elements.finalPromptPreview.value = '';
        return;
    }

    const prompt = appState.assembledBricks
        .map(brick => {
            const title = brick.title || brick.brickName || 'Untitled Brick';
            const content = brick.content || brick.promptText || 'No content';
            return `${title}: ${content}`;
        })
        .join('\n\n');

    elements.finalPromptPreview.value = `Create a story incorporating these elements:\n\n${prompt}`;
}

/**
 * Show add brick modal
 */
function showAddBrickModal() {
    elements.modalOverlay.classList.remove('hidden');
    elements.brickNameInput.focus();
}

/**
 * Hide add brick modal
 */
function hideAddBrickModal() {
    elements.modalOverlay.classList.add('hidden');
    clearBrickForm();
}

/**
 * Handle modal overlay click (close modal when clicking outside)
 */
function handleModalOverlayClick(e) {
    if (e.target === elements.modalOverlay) {
        hideAddBrickModal();
    }
}

/**
 * Handle keyboard events
 */
function handleKeyDown(e) {
    // Close modal with ESC key
    if (e.key === 'Escape' && !elements.modalOverlay.classList.contains('hidden')) {
        hideAddBrickModal();
    }
}

/**
 * Clear brick form fields
 */
function clearBrickForm() {
    elements.brickNameInput.value = '';
    elements.characterArchetypeInput.value = '';
    elements.characterBackstoryInput.value = '';
    elements.characterGoalInput.value = '';
}

/**
 * Handle brick form submission
 */
async function handleBrickFormSubmit(e) {
    e.preventDefault();

    // Get form values
    const brickName = elements.brickNameInput.value.trim();
    const archetype = elements.characterArchetypeInput.value.trim();
    const backstory = elements.characterBackstoryInput.value.trim();
    const goal = elements.characterGoalInput.value.trim();

    // Validate required fields
    if (!brickName || !archetype || !backstory || !goal) {
        alert('Please fill in all fields');
        return;
    }

    // Show loading state
    const submitBtn = elements.brickForm.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.disabled = true;
    submitBtn.textContent = 'Saving...';

    // Create new brick object
    const newBrick = {
        brickType: 'Character',
        brickName: brickName,
        data: {
            archetype: archetype,
            backstory: backstory,
            goal: goal
        },
        promptText: `The character is ${brickName}, a ${archetype}. Backstory: ${backstory}. Their primary goal is: ${goal}.`,
        // Additional metadata for compatibility with existing system
        title: brickName,
        content: `The character is ${brickName}, a ${archetype}. Backstory: ${backstory}. Their primary goal is: ${goal}.`,
        type: 'character',
        tags: [archetype.toLowerCase(), 'character'],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
    };

    try {
        await saveBrick(newBrick);
        hideAddBrickModal();
        console.log('Brick saved successfully:', newBrick);
    } catch (error) {
        console.error('Error saving brick:', error);
        alert('Failed to save brick. Please try again.');
    } finally {
        // Restore button state
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;
    }
}

/**
 * Save brick to Firestore
 */
async function saveBrick(brickData) {
    if (!firebaseInitialized) {
        console.log('Firebase not initialized, adding to sample data');
        // Add to local state for demo mode
        const brickWithId = {
            id: 'brick-' + Date.now(),
            ...brickData
        };
        state.bricks.push(brickWithId);
        appState.bricks.push(brickWithId);
        renderBrickLibrary();
        return brickWithId;
    }

    if (!appState.userId) {
        throw new Error('No user ID available');
    }

    try {
        const bricksPath = `artifacts/${appId}/users/${appState.userId}/bricks`;
        const bricksCollection = collection(db, bricksPath);

        const docRef = await addDoc(bricksCollection, brickData);
        console.log('Brick saved to Firestore with ID:', docRef.id);

        return {
            id: docRef.id,
            ...brickData
        };
    } catch (error) {
        console.error('Error saving brick to Firestore:', error);
        throw error;
    }
}

/**
 * Generate story using AI
 */
async function generateStory() {
    if (appState.assembledBricks.length === 0) {
        alert('Please add some bricks to your prompt first');
        return;
    }
    
    if (appState.isGenerating) {
        return;
    }
    
    appState.isGenerating = true;
    elements.generateStoryBtn.disabled = true;
    elements.generateStoryBtn.textContent = 'Generating...';
    
    // Show loading state
    elements.aiOutputContent.innerHTML = `
        <div class="border border-slate-600 rounded-lg p-6 h-full flex items-center justify-center">
            <div class="text-center">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
                <p class="text-slate-400">Generating your story...</p>
            </div>
        </div>
    `;
    
    try {
        const response = await fetch('api/ai-generate.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                prompt: elements.finalPromptPreview.value,
                bricks: appState.assembledBricks
            })
        });
        
        if (response.ok) {
            const result = await response.json();
            displayGeneratedStory(result.story);
        } else {
            throw new Error('Failed to generate story');
        }
    } catch (error) {
        console.error('Error generating story:', error);
        displayError('Failed to generate story. Please try again.');
    } finally {
        appState.isGenerating = false;
        elements.generateStoryBtn.disabled = false;
        elements.generateStoryBtn.textContent = 'Generate Story';
    }
}

/**
 * Display generated story
 */
function displayGeneratedStory(story) {
    elements.aiOutputContent.innerHTML = `
        <div class="border border-slate-600 rounded-lg p-6 h-full">
            <div class="prose prose-invert max-w-none">
                <div class="whitespace-pre-wrap text-slate-200">${story}</div>
            </div>
        </div>
    `;
}

/**
 * Display error message
 */
function displayError(message) {
    elements.aiOutputContent.innerHTML = `
        <div class="border border-red-600 rounded-lg p-6 h-full flex items-center justify-center">
            <div class="text-center">
                <p class="text-red-400">${message}</p>
            </div>
        </div>
    `;
}

// Export for use in other modules
export { appState, loadBricks, renderBrickLibrary };
