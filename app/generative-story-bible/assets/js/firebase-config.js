/**
 * Firebase Configuration
 * 
 * Replace the placeholder values below with your actual Firebase project configuration.
 * You can find these values in your Firebase Console > Project Settings > General tab.
 */

// Firebase configuration object
export const firebaseConfig = {
    apiKey: "your-api-key-here",
    authDomain: "your-project-id.firebaseapp.com",
    projectId: "your-project-id",
    storageBucket: "your-project-id.appspot.com",
    messagingSenderId: "123456789012",
    appId: "1:123456789012:web:abcdef123456789012345678"
};

// App ID for Firestore collection paths
export const appId = "generative-story-bible";

/**
 * Instructions for setting up Firebase:
 * 
 * 1. Go to https://console.firebase.google.com/
 * 2. Create a new project or select an existing one
 * 3. Click on "Add app" and select "Web"
 * 4. Register your app with a nickname
 * 5. Copy the configuration object and replace the values above
 * 6. Enable Firestore Database in your Firebase console:
 *    - Go to Firestore Database
 *    - Click "Create database"
 *    - Choose "Start in test mode" for development
 * 7. Enable Authentication:
 *    - Go to Authentication
 *    - Click "Get started"
 *    - Enable "Anonymous" sign-in method
 * 
 * Security Rules for Firestore (for development):
 * ```
 * rules_version = '2';
 * service cloud.firestore {
 *   match /databases/{database}/documents {
 *     // Allow read/write access to artifacts collection for authenticated users
 *     match /artifacts/{appId}/users/{userId}/{document=**} {
 *       allow read, write: if request.auth != null && request.auth.uid == userId;
 *     }
 *   }
 * }
 * ```
 */
