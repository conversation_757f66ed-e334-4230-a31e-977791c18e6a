/**
 * Generative Story Bible - Custom Styles
 * 
 * Additional styles for the Story Bible app that complement Tailwind CSS
 */

/* Custom animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Brick card animations */
.brick-card {
    animation: fadeIn 0.3s ease-out;
    transition: all 0.2s ease;
}

.brick-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.brick-card.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

/* Drop zone styles */
.drop-zone {
    transition: all 0.3s ease;
}

.drop-zone.drag-over {
    border-color: #3b82f6;
    background-color: rgba(59, 130, 246, 0.1);
    transform: scale(1.02);
}

/* Assembled brick styles */
.assembled-brick {
    animation: slideIn 0.3s ease-out;
    position: relative;
}

.assembled-brick::before {
    content: '';
    position: absolute;
    left: -4px;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(to bottom, #3b82f6, #1d4ed8);
    border-radius: 2px;
}

/* Loading states */
.loading {
    animation: pulse 1.5s ease-in-out infinite;
}

.spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Story output styles */
.story-output {
    line-height: 1.8;
    font-family: 'Inter', sans-serif;
}

.story-output p {
    margin-bottom: 1rem;
}

.story-output h1, .story-output h2, .story-output h3 {
    margin-top: 1.5rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

/* Brick type indicators */
.brick-type-character { border-left: 4px solid #10b981; }
.brick-type-setting { border-left: 4px solid #f59e0b; }
.brick-type-plot { border-left: 4px solid #ef4444; }
.brick-type-object { border-left: 4px solid #8b5cf6; }
.brick-type-conflict { border-left: 4px solid #f97316; }
.brick-type-theme { border-left: 4px solid #06b6d4; }
.brick-type-dialogue { border-left: 4px solid #ec4899; }
.brick-type-other { border-left: 4px solid #6b7280; }

/* Responsive adjustments */
@media (max-width: 768px) {
    .brick-library-container {
        width: 100%;
        max-height: 300px;
    }
    
    .three-column-layout {
        flex-direction: column;
    }
    
    .prompt-assembler-container,
    .ai-output-container {
        min-height: 400px;
    }
}

/* Scrollbar styling */
.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #1e293b;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #475569;
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #64748b;
}

/* Focus states */
.focus-ring:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
}

/* Button states */
.btn-primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    transition: all 0.2s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2563eb, #1e40af);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.btn-primary:active {
    transform: translateY(0);
}

/* Toast notifications */
.toast {
    animation: slideIn 0.3s ease-out;
    transition: all 0.3s ease;
}

.toast.hiding {
    opacity: 0;
    transform: translateX(100%);
}

/* Modal styles */
.modal-backdrop {
    backdrop-filter: blur(4px);
    animation: fadeIn 0.2s ease-out;
}

.modal-content {
    animation: fadeIn 0.3s ease-out;
    transform: scale(0.95);
    animation: modalSlideIn 0.3s ease-out forwards;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.95) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Modal form styles */
#brick-modal {
    animation: modalSlideIn 0.3s ease-out;
}

#modal-overlay.hidden {
    display: none;
}

/* Form input focus states */
#brick-form input:focus,
#brick-form textarea:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Form validation styles */
#brick-form input:invalid,
#brick-form textarea:invalid {
    border-color: #ef4444;
}

#brick-form input:valid,
#brick-form textarea:valid {
    border-color: #10b981;
}

/* Utility classes */
.text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.gradient-border {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    padding: 1px;
    border-radius: 8px;
}

.gradient-border > div {
    background: #1e293b;
    border-radius: 7px;
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .story-output {
        color: black !important;
        background: white !important;
    }
}
