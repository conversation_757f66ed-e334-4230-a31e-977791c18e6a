# Testing Guide for Prompt 5.1: Call Gemini API and Display Output

This guide helps you test the AI story generation functionality using the Gemini API.

## What Was Implemented

### 1. Event Listener Setup
- ✅ `#generate-story-btn` already has click event listener
- ✅ Event listener calls `generateStory()` function

### 2. Generation Logic Updates
- ✅ Gets final prompt text from `#final-prompt-preview` textarea
- ✅ Validates prompt is not empty before proceeding
- ✅ Shows loading indicator in `#ai-output-content` div
- ✅ Makes direct fetch call to Gemini API
- ✅ Handles API response and displays generated story

### 3. API Integration
- ✅ Uses `gemini-2.0-flash` model
- ✅ Proper API call structure with contents array
- ✅ Secure API key handling with multiple fallback options
- ✅ Comprehensive error handling

### 4. Response Handling
- ✅ Validates response structure from Gemini API
- ✅ Extracts generated text from `result.candidates[0].content.parts[0].text`
- ✅ Updates `#ai-output-content` with formatted story
- ✅ Preserves formatting with newline to `<br>` conversion

## Prerequisites

### API Key Setup
Before testing, you need a Gemini API key. See `GEMINI_API_SETUP.md` for detailed instructions.

**Quick Setup for Testing:**
1. Get API key from [Google AI Studio](https://aistudio.google.com/)
2. Open `assets/js/app.js`
3. Find `getGeminiApiKey()` function
4. Set your API key in the `devApiKey` variable:
   ```javascript
   const devApiKey = "your-actual-api-key-here";
   ```

## Testing Steps

### Step 1: Basic Functionality Test
1. **Open the Generative Story Bible app**
2. **Create a character brick:**
   ```
   Brick Name: Test Hero
   Archetype: Brave Knight
   Backstory: A young warrior seeking adventure
   Goal: To prove their worth in battle
   ```
3. **Drag the brick to the Character slot**
4. **Verify prompt preview shows the character information**
5. **Click "Generate Story" button**

### Step 2: Loading State Verification
**Expected behavior when clicking "Generate Story":**
1. Button text changes to "Generating..."
2. Button becomes disabled
3. AI output area shows loading spinner with "Generating your story..." text
4. Loading state persists until API response or error

### Step 3: Successful Generation Test
**With valid API key and prompt:**
1. API call should complete successfully
2. Loading state should disappear
3. Generated story should appear in the AI output area
4. Button should return to "Generate Story" and be enabled
5. Story text should be properly formatted with line breaks

### Step 4: Error Handling Tests

#### Test 4a: Empty Prompt
1. Clear all bricks from assembler slots
2. Click "Generate Story"
3. Should show alert: "Please add some bricks to your prompt first"
4. Should not make API call

#### Test 4b: Missing API Key
1. Ensure API key is not configured (empty `devApiKey`)
2. Try to generate story
3. Should show error: "Failed to generate story: Gemini API key not configured..."

#### Test 4c: Invalid API Key
1. Set an invalid API key (e.g., "invalid-key")
2. Try to generate story
3. Should show error: "Failed to generate story: API request failed with status 401"

### Step 5: Multiple Generation Test
1. Generate a story successfully
2. Modify the prompt (add/remove bricks)
3. Generate another story
4. Verify both generations work correctly
5. Check that previous story is replaced with new one

## Expected API Call Structure

### Request
```javascript
{
  contents: [{
    role: "user",
    parts: [{ 
      text: "Create a story incorporating these elements:\n\nThe character is Test Hero, a Brave Knight. Backstory: A young warrior seeking adventure. Their primary goal is: To prove their worth in battle."
    }]
  }]
}
```

### Response
```javascript
{
  candidates: [{
    content: {
      parts: [{
        text: "Once upon a time, in a kingdom far away, there lived a young warrior named Test Hero..."
      }]
    }
  }]
}
```

## Browser Console Testing

### Check API Key Configuration
```javascript
// In browser console:
try {
  const apiKey = getGeminiApiKey();
  console.log('API Key configured:', !!apiKey);
  console.log('API Key length:', apiKey.length);
} catch (error) {
  console.log('API Key error:', error.message);
}
```

### Manual API Test
```javascript
// Test API call manually:
const testPrompt = "Write a short story about a brave knight.";
const payload = {
  contents: [{
    role: "user",
    parts: [{ text: testPrompt }]
  }]
};

fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=YOUR_API_KEY`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(payload)
})
.then(response => response.json())
.then(result => console.log('API Response:', result))
.catch(error => console.error('API Error:', error));
```

### Check Generation State
```javascript
// Check if generation is in progress:
console.log('Is generating:', appState.isGenerating);
console.log('Button state:', elements.generateStoryBtn.disabled);
console.log('Button text:', elements.generateStoryBtn.textContent);
```

## Expected Visual States

### Loading State
```html
<div class="border border-slate-600 rounded-lg p-6 h-full flex items-center justify-center">
    <div class="text-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
        <p class="text-slate-400">Generating your story...</p>
    </div>
</div>
```

### Success State
```html
<div class="border border-slate-600 rounded-lg p-6 h-full">
    <div class="prose prose-invert max-w-none">
        <div class="text-slate-200 leading-relaxed">
            Generated story content with proper<br>line breaks and formatting...
        </div>
    </div>
</div>
```

### Error State
```html
<div class="border border-red-600 rounded-lg p-6 h-full flex items-center justify-center">
    <div class="text-center">
        <p class="text-red-400">Failed to generate story: [Error message]</p>
    </div>
</div>
```

## Common Issues and Solutions

### Issue 1: "API key not configured" Error
**Solution**: Set up your Gemini API key using one of the methods in `GEMINI_API_SETUP.md`

### Issue 2: CORS Errors
**Cause**: Browser blocking cross-origin requests
**Solution**: Gemini API supports CORS, but check browser console for specific errors

### Issue 3: "Invalid response format" Error
**Cause**: API response structure changed or network issues
**Solution**: Check network tab in browser dev tools, verify API response structure

### Issue 4: Button Stuck in "Generating..." State
**Cause**: JavaScript error during API call
**Solution**: Check browser console for errors, refresh page to reset state

### Issue 5: Story Not Displaying
**Cause**: HTML escaping or formatting issues
**Solution**: Check browser console, verify `displayGeneratedStory()` function

## Success Criteria

✅ **Event Listener**: Generate button triggers story generation
✅ **Prompt Validation**: Empty prompts are rejected with alert
✅ **Loading State**: Proper loading indicator during generation
✅ **API Integration**: Direct calls to Gemini API work correctly
✅ **Response Handling**: Generated stories display properly
✅ **Error Handling**: API errors show user-friendly messages
✅ **Formatting**: Story text preserves line breaks and formatting
✅ **State Management**: Button states update correctly
✅ **Security**: API key handled securely without exposure

## Sample Test Scenarios

### Scenario 1: Complete Workflow
1. Create character brick → Place in slot → Generate story → Verify output

### Scenario 2: Error Recovery
1. Try generation without API key → See error → Configure API key → Retry successfully

### Scenario 3: Multiple Generations
1. Generate story → Modify prompt → Generate again → Verify new story replaces old

### Scenario 4: Edge Cases
1. Very long prompts → Should work within API limits
2. Special characters in prompts → Should be properly escaped
3. Network interruption → Should show appropriate error

## Next Steps

After successful testing:
1. Verify API key security measures
2. Test with various prompt types and lengths
3. Monitor API usage and costs
4. Consider implementing rate limiting
5. Ready for production deployment or additional features

## Troubleshooting Checklist

- [ ] API key is correctly configured
- [ ] Internet connection is working
- [ ] Browser console shows no JavaScript errors
- [ ] Gemini API service is operational
- [ ] Prompt assembler has valid content
- [ ] Button event listeners are attached
- [ ] Response parsing handles API format correctly
