# Testing Guide for Prompt 3.2: Render Bricks in the Library

This guide helps you test the updated `renderBrickLibrary()` function and drag-and-drop functionality.

## What Was Implemented

### 1. Updated `renderBrickLibrary()` Function
- Now iterates over `state.bricks` array (as requested)
- Creates card elements directly in the function
- Uses proper Tailwind CSS styling
- Adds required data attributes and draggable functionality

### 2. Event Delegation for Drag and Drop
- Added `dragstart` event listener to `#brick-library-list` container
- Uses event delegation instead of individual listeners
- <PERSON><PERSON>ly handles `data-brick-id` attributes

### 3. Enhanced Card Styling
- Clean, minimal card design
- Hover effects and transitions
- Proper spacing and typography

## Testing Steps

### Step 1: Verify Basic Rendering
1. Open the Generative Story Bible app
2. Check the brick library (left panel)
3. You should see:
   - Sample bricks displayed as cards
   - Each card has a brick name (h3) and type (p)
   - Cards have proper styling (slate-700 background, rounded corners)

### Step 2: Test Card Structure
**Expected Card HTML Structure:**
```html
<div class="brick-card bg-slate-700 border border-slate-600 rounded-lg p-3 mb-3 cursor-move transition-all duration-200 hover:bg-slate-600" 
     data-brick-id="brick-1" 
     draggable="true">
    <h3 class="font-medium text-slate-100 text-sm mb-1">Hero Character</h3>
    <p class="text-xs text-slate-400">character</p>
</div>
```

### Step 3: Test Drag Functionality
1. **Hover over a brick card**
   - Should show hover effect (background changes to slate-600)
   - Cursor should show move cursor

2. **Start dragging a brick**
   - Card should become semi-transparent (opacity 0.5)
   - Card should get "dragging" class
   - Should slightly rotate and scale down

3. **End dragging**
   - Card should return to normal opacity
   - "dragging" class should be removed

### Step 4: Test Drop Functionality
1. **Drag a brick to the prompt assembler (center panel)**
   - Should accept the drop
   - Brick should appear in the assembled bricks area
   - Prompt preview should update

2. **Try dragging the same brick again**
   - Should not allow duplicate bricks in assembler

### Step 5: Test Console Logging
**Open browser developer console and check for:**
```
Current state.bricks: [...]
Current appState.bricks: [...]
```

### Step 6: Test New Brick Creation
1. **Create a new character brick using the modal**
2. **Verify the new brick appears in the library**
   - Should render with the new card format
   - Should be draggable immediately
   - Should show correct brick name and "Character" type

## Expected Visual Appearance

### Card Design
- **Background**: Dark slate (slate-700)
- **Border**: Subtle slate border (slate-600)
- **Padding**: Consistent spacing (p-3)
- **Margins**: Bottom margin between cards (mb-3)
- **Corners**: Rounded (rounded-lg)

### Typography
- **Brick Name**: White text, medium weight, small size
- **Brick Type**: Light gray text, extra small size, capitalized

### Hover Effects
- **Background**: Changes to lighter slate (slate-600)
- **Transform**: Slight upward movement
- **Transition**: Smooth 200ms transition

### Drag States
- **Dragging**: Semi-transparent, rotated, scaled down
- **Cursor**: Move cursor when hovering

## Browser Console Tests

### Check Event Delegation
1. Open browser console
2. Run: `document.querySelector('#brick-library-list').addEventListener('dragstart', (e) => console.log('Drag started:', e.target))`
3. Drag a brick and verify the event fires

### Check Data Attributes
1. In console, run: `document.querySelectorAll('[data-brick-id]')`
2. Should return all brick cards with proper IDs

### Check State Synchronization
1. In console, run: `window.state` (if exposed) or check console logs
2. Verify `state.bricks` contains the expected brick data

## Common Issues and Solutions

### Cards Not Appearing
- Check console for JavaScript errors
- Verify `state.bricks` has data
- Ensure `renderBrickLibrary()` is being called

### Drag Not Working
- Check that `draggable="true"` is set on cards
- Verify event delegation is properly attached
- Check for JavaScript errors in console

### Styling Issues
- Ensure Tailwind CSS is loaded
- Check that CSS classes are applied correctly
- Verify custom CSS is loading

### Data Attribute Issues
- Check that `data-brick-id` is set correctly
- Verify brick IDs are unique
- Ensure IDs match between state and DOM

## Success Criteria

✅ **Rendering**: Bricks display as styled cards
✅ **Structure**: Cards have proper HTML structure and classes
✅ **Data Attributes**: Each card has correct `data-brick-id`
✅ **Draggable**: Cards have `draggable="true"` attribute
✅ **Event Delegation**: Drag events work through container listener
✅ **Visual Feedback**: Hover and drag states work correctly
✅ **Drop Functionality**: Bricks can be dropped in assembler
✅ **State Management**: Uses `state.bricks` for rendering
✅ **Console Logging**: Proper debug information displayed

## Sample Test Data

**Expected Sample Bricks:**
```javascript
[
  {
    id: 'brick-1',
    brickName: 'Hero Character',
    brickType: 'character',
    // ... other properties
  },
  {
    id: 'brick-2', 
    brickName: 'Dark Forest Setting',
    brickType: 'setting',
    // ... other properties
  },
  {
    id: 'brick-3',
    brickName: 'Quest Plot', 
    brickType: 'plot',
    // ... other properties
  }
]
```

## Next Steps

After successful testing:
1. Verify all drag and drop functionality works
2. Test with newly created bricks
3. Ensure real-time updates work with Firebase
4. Ready for next phase of development

## Troubleshooting

**If bricks don't appear:**
- Check `state.bricks` in console
- Verify `renderBrickLibrary()` is called
- Check for JavaScript errors

**If drag doesn't work:**
- Verify event listeners are attached
- Check `draggable="true"` attribute
- Test event delegation manually

**If styling is wrong:**
- Ensure Tailwind CSS is loaded
- Check CSS class names
- Verify custom CSS is applied
