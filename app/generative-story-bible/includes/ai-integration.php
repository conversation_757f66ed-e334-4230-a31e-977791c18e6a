<?php
/**
 * Generative Story Bible - AI Integration Class
 * 
 * This class handles AI integration for story generation
 * using the dashboard's AI configuration.
 */

class StoryBibleAIIntegration {
    private $config;
    private $aiClient;
    
    public function __construct($aiConfig) {
        $this->config = $aiConfig;
        $this->initializeAIClient();
    }
    
    /**
     * Initialize AI client using dashboard configuration
     */
    private function initializeAIClient() {
        // Use the dashboard's AI client if available
        if (file_exists(__DIR__ . '/../../../includes/ai_client.php')) {
            require_once __DIR__ . '/../../../includes/ai_client.php';
            $this->aiClient = new AIClient();
        }
    }
    
    /**
     * Test AI connection
     */
    public function testConnection() {
        try {
            if ($this->aiClient) {
                return $this->aiClient->testConnection();
            }
            return false;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Generate story using AI
     */
    public function generateStory($prompt, $bricks = []) {
        if (!$this->aiClient) {
            throw new Exception('AI client not available');
        }
        
        // Enhance the prompt with story bible context
        $enhancedPrompt = $this->buildStoryPrompt($prompt, $bricks);
        
        try {
            $response = $this->aiClient->sendMessage(
                $enhancedPrompt,
                [
                    'model' => $this->config['default_model'] ?? 'gpt-3.5-turbo',
                    'max_tokens' => $this->config['max_tokens'] ?? 8000,
                    'temperature' => $this->config['temperature'] ?? 0.7,
                    'system_prompt' => $this->config['system_prompt']
                ]
            );
            
            return $response['content'] ?? $response['message'] ?? 'Story generation failed';
        } catch (Exception $e) {
            throw new Exception('AI story generation failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Build enhanced story prompt
     */
    private function buildStoryPrompt($basePrompt, $bricks) {
        $prompt = "You are a creative storyteller. ";
        $prompt .= "Create an engaging, well-structured story using the following elements:\n\n";
        
        if (!empty($bricks)) {
            $prompt .= "STORY ELEMENTS (Bricks):\n";
            foreach ($bricks as $brick) {
                $prompt .= "- {$brick['title']} ({$brick['type']}): {$brick['content']}\n";
            }
            $prompt .= "\n";
        }
        
        $prompt .= "INSTRUCTIONS:\n";
        $prompt .= $basePrompt . "\n\n";
        
        $prompt .= "Please create a compelling narrative that:\n";
        $prompt .= "1. Incorporates all the provided story elements naturally\n";
        $prompt .= "2. Has a clear beginning, middle, and end\n";
        $prompt .= "3. Develops characters and settings with depth\n";
        $prompt .= "4. Maintains consistent tone and style\n";
        $prompt .= "5. Is engaging and well-paced\n\n";
        
        $prompt .= "Write the story in a narrative format, approximately 500-1000 words.";
        
        return $prompt;
    }
    
    /**
     * Generate brick suggestions based on existing bricks
     */
    public function suggestBricks($existingBricks, $theme = '') {
        if (!$this->aiClient) {
            throw new Exception('AI client not available');
        }
        
        $prompt = "Based on the following existing story elements, suggest 3-5 new complementary story bricks:\n\n";
        
        if (!empty($existingBricks)) {
            foreach ($existingBricks as $brick) {
                $prompt .= "- {$brick['title']} ({$brick['type']}): {$brick['content']}\n";
            }
        }
        
        if ($theme) {
            $prompt .= "\nTheme/Genre: {$theme}\n";
        }
        
        $prompt .= "\nFor each suggestion, provide:\n";
        $prompt .= "1. Title (brief, descriptive)\n";
        $prompt .= "2. Type (character, setting, plot, object, conflict, etc.)\n";
        $prompt .= "3. Content (1-2 sentence description)\n";
        $prompt .= "4. Tags (3-5 relevant keywords)\n\n";
        $prompt .= "Format as JSON array with objects containing: title, type, content, tags";
        
        try {
            $response = $this->aiClient->sendMessage(
                $prompt,
                [
                    'model' => $this->config['default_model'] ?? 'gpt-3.5-turbo',
                    'max_tokens' => 2000,
                    'temperature' => 0.8
                ]
            );
            
            $content = $response['content'] ?? $response['message'] ?? '';
            
            // Try to extract JSON from the response
            if (preg_match('/\[.*\]/s', $content, $matches)) {
                $suggestions = json_decode($matches[0], true);
                if ($suggestions) {
                    return $suggestions;
                }
            }
            
            // Fallback: parse text response
            return $this->parseTextSuggestions($content);
            
        } catch (Exception $e) {
            throw new Exception('AI brick suggestion failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Parse text suggestions as fallback
     */
    private function parseTextSuggestions($text) {
        $suggestions = [];
        $lines = explode("\n", $text);
        $currentBrick = null;
        
        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) continue;
            
            if (preg_match('/^\d+\.\s*(.+)/', $line, $matches)) {
                if ($currentBrick) {
                    $suggestions[] = $currentBrick;
                }
                $currentBrick = [
                    'title' => $matches[1],
                    'type' => 'element',
                    'content' => '',
                    'tags' => []
                ];
            } elseif ($currentBrick && preg_match('/Type:\s*(.+)/i', $line, $matches)) {
                $currentBrick['type'] = strtolower(trim($matches[1]));
            } elseif ($currentBrick && preg_match('/Content:\s*(.+)/i', $line, $matches)) {
                $currentBrick['content'] = trim($matches[1]);
            } elseif ($currentBrick && preg_match('/Tags:\s*(.+)/i', $line, $matches)) {
                $tags = explode(',', $matches[1]);
                $currentBrick['tags'] = array_map('trim', $tags);
            }
        }
        
        if ($currentBrick) {
            $suggestions[] = $currentBrick;
        }
        
        return $suggestions;
    }
    
    /**
     * Analyze story for feedback
     */
    public function analyzeStory($story, $bricks = []) {
        if (!$this->aiClient) {
            throw new Exception('AI client not available');
        }
        
        $prompt = "Analyze the following story and provide constructive feedback:\n\n";
        $prompt .= "STORY:\n{$story}\n\n";
        
        if (!empty($bricks)) {
            $prompt .= "ORIGINAL STORY ELEMENTS:\n";
            foreach ($bricks as $brick) {
                $prompt .= "- {$brick['title']}: {$brick['content']}\n";
            }
            $prompt .= "\n";
        }
        
        $prompt .= "Please provide feedback on:\n";
        $prompt .= "1. How well the story elements were integrated\n";
        $prompt .= "2. Story structure and pacing\n";
        $prompt .= "3. Character development\n";
        $prompt .= "4. Areas for improvement\n";
        $prompt .= "5. Overall strengths\n\n";
        $prompt .= "Keep feedback constructive and specific.";
        
        try {
            $response = $this->aiClient->sendMessage(
                $prompt,
                [
                    'model' => $this->config['default_model'] ?? 'gpt-3.5-turbo',
                    'max_tokens' => 1500,
                    'temperature' => 0.3
                ]
            );
            
            return $response['content'] ?? $response['message'] ?? 'Analysis failed';
        } catch (Exception $e) {
            throw new Exception('Story analysis failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Get available AI models
     */
    public function getAvailableModels() {
        return $this->config['models'] ?? ['gpt-3.5-turbo'];
    }
    
    /**
     * Get AI configuration
     */
    public function getConfig() {
        return $this->config;
    }
    
    /**
     * Update AI configuration
     */
    public function updateConfig($newConfig) {
        $this->config = array_merge($this->config, $newConfig);
    }
}
?>
