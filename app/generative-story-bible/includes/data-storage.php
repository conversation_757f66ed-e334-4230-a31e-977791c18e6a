<?php
/**
 * Generative Story Bible - Data Storage Class
 * 
 * This class handles flat file data storage for the Story Bible app
 * following the AI Dashboard patterns.
 */

class StoryBibleDataStorage {
    private $dataDir;
    
    public function __construct($dataDir) {
        $this->dataDir = $dataDir;
        $this->initializeDirectories();
    }
    
    /**
     * Initialize data directories
     */
    public function initializeDirectories() {
        $directories = [
            $this->dataDir,
            $this->dataDir . '/bricks',
            $this->dataDir . '/stories',
            $this->dataDir . '/user_data',
            $this->dataDir . '/logs'
        ];
        
        foreach ($directories as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
        }
        
        // Protect data directory
        $htaccessFile = $this->dataDir . '/.htaccess';
        if (!file_exists($htaccessFile)) {
            file_put_contents($htaccessFile, "Deny from all\n");
        }
    }
    
    /**
     * Save user data
     */
    public function saveUserData($userId, $type, $data) {
        $userDir = $this->dataDir . '/user_data/' . $userId;
        if (!is_dir($userDir)) {
            mkdir($userDir, 0755, true);
        }
        
        $file = $userDir . '/' . $type . '.json';
        return file_put_contents($file, json_encode($data, JSON_PRETTY_PRINT));
    }
    
    /**
     * Load user data
     */
    public function loadUserData($userId, $type) {
        $file = $this->dataDir . '/user_data/' . $userId . '/' . $type . '.json';
        if (!file_exists($file)) {
            return null;
        }
        
        $content = file_get_contents($file);
        return json_decode($content, true);
    }
    
    /**
     * Save global data
     */
    public function saveData($type, $key, $data) {
        $dir = $this->dataDir . '/' . $type;
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
        
        $file = $dir . '/' . $key . '.json';
        return file_put_contents($file, json_encode($data, JSON_PRETTY_PRINT));
    }
    
    /**
     * Load global data
     */
    public function loadData($type, $key) {
        $file = $this->dataDir . '/' . $type . '/' . $key . '.json';
        if (!file_exists($file)) {
            return null;
        }
        
        $content = file_get_contents($file);
        return json_decode($content, true);
    }
    
    /**
     * List data files
     */
    public function listData($type) {
        $dir = $this->dataDir . '/' . $type;
        if (!is_dir($dir)) {
            return [];
        }
        
        $files = glob($dir . '/*.json');
        $data = [];
        
        foreach ($files as $file) {
            $key = basename($file, '.json');
            $content = file_get_contents($file);
            $data[$key] = json_decode($content, true);
        }
        
        return $data;
    }
    
    /**
     * Delete data
     */
    public function deleteData($type, $key) {
        $file = $this->dataDir . '/' . $type . '/' . $key . '.json';
        if (file_exists($file)) {
            return unlink($file);
        }
        return false;
    }
    
    /**
     * Append to log
     */
    public function appendToLog($logType, $entry) {
        $logFile = $this->dataDir . '/logs/' . $logType . '.json';
        
        $logs = [];
        if (file_exists($logFile)) {
            $content = file_get_contents($logFile);
            $logs = json_decode($content, true) ?? [];
        }
        
        $logs[] = $entry;
        
        // Keep only last 1000 entries
        if (count($logs) > 1000) {
            $logs = array_slice($logs, -1000);
        }
        
        return file_put_contents($logFile, json_encode($logs, JSON_PRETTY_PRINT));
    }
    
    /**
     * Get log entries
     */
    public function getLogEntries($logType, $limit = 100) {
        $logFile = $this->dataDir . '/logs/' . $logType . '.json';
        
        if (!file_exists($logFile)) {
            return [];
        }
        
        $content = file_get_contents($logFile);
        $logs = json_decode($content, true) ?? [];
        
        return array_slice($logs, -$limit);
    }
    
    /**
     * Clear logs
     */
    public function clearLogs() {
        $logDir = $this->dataDir . '/logs';
        if (is_dir($logDir)) {
            $files = glob($logDir . '/*.json');
            foreach ($files as $file) {
                unlink($file);
            }
        }
    }
    
    /**
     * Get data directory size
     */
    public function getDataSize() {
        return $this->getDirectorySize($this->dataDir);
    }
    
    /**
     * Get directory size recursively
     */
    private function getDirectorySize($directory) {
        $size = 0;
        
        if (is_dir($directory)) {
            $files = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS)
            );
            
            foreach ($files as $file) {
                $size += $file->getSize();
            }
        }
        
        return $size;
    }
    
    /**
     * Backup data
     */
    public function backupData($backupPath) {
        if (!is_dir(dirname($backupPath))) {
            mkdir(dirname($backupPath), 0755, true);
        }
        
        // Create a zip archive of the data directory
        $zip = new ZipArchive();
        if ($zip->open($backupPath, ZipArchive::CREATE) === TRUE) {
            $files = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($this->dataDir, RecursiveDirectoryIterator::SKIP_DOTS),
                RecursiveIteratorIterator::LEAVES_ONLY
            );
            
            foreach ($files as $file) {
                $filePath = $file->getRealPath();
                $relativePath = substr($filePath, strlen($this->dataDir) + 1);
                $zip->addFile($filePath, $relativePath);
            }
            
            $zip->close();
            return true;
        }
        
        return false;
    }
    
    /**
     * Restore data from backup
     */
    public function restoreData($backupPath) {
        if (!file_exists($backupPath)) {
            return false;
        }
        
        $zip = new ZipArchive();
        if ($zip->open($backupPath) === TRUE) {
            // Clear existing data
            $this->clearAllData();
            
            // Extract backup
            $zip->extractTo($this->dataDir);
            $zip->close();
            
            return true;
        }
        
        return false;
    }
    
    /**
     * Clear all data
     */
    private function clearAllData() {
        $directories = ['bricks', 'stories', 'user_data', 'logs'];
        
        foreach ($directories as $dir) {
            $fullPath = $this->dataDir . '/' . $dir;
            if (is_dir($fullPath)) {
                $files = glob($fullPath . '/*');
                foreach ($files as $file) {
                    if (is_file($file)) {
                        unlink($file);
                    } elseif (is_dir($file)) {
                        $this->removeDirectory($file);
                    }
                }
            }
        }
    }
    
    /**
     * Remove directory recursively
     */
    private function removeDirectory($dir) {
        if (is_dir($dir)) {
            $files = array_diff(scandir($dir), ['.', '..']);
            foreach ($files as $file) {
                $path = $dir . '/' . $file;
                if (is_dir($path)) {
                    $this->removeDirectory($path);
                } else {
                    unlink($path);
                }
            }
            rmdir($dir);
        }
    }
}
?>
