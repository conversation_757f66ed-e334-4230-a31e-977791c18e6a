<?php
/**
 * Generative Story Bible - Main App Class
 * 
 * This class handles the core functionality of the Story Bible app
 * and integrates with the AI Dashboard framework.
 */

require_once __DIR__ . '/data-storage.php';
require_once __DIR__ . '/ai-integration.php';

class GenerativeStoryBibleApp {
    private $config;
    private $dataStorage;
    private $aiIntegration;
    private $appPath;
    
    public function __construct() {
        $this->appPath = __DIR__ . '/..';
        $this->loadConfig();
        $this->dataStorage = new StoryBibleDataStorage($this->appPath . '/data');
        $this->aiIntegration = new StoryBibleAIIntegration($this->config['ai']);
        $this->initializeApp();
    }
    
    /**
     * Load app configuration
     */
    private function loadConfig() {
        $configFile = $this->appPath . '/config.json';
        if (file_exists($configFile)) {
            $this->config = json_decode(file_get_contents($configFile), true);
        } else {
            throw new Exception('App configuration file not found');
        }
    }
    
    /**
     * Initialize the application
     */
    private function initializeApp() {
        // Ensure data directories exist
        $this->dataStorage->initializeDirectories();
        
        // Log app initialization
        $this->logActivity('app.initialized', 'Story Bible app initialized');
    }
    
    /**
     * Get app configuration
     */
    public function getConfig() {
        return $this->config;
    }
    
    /**
     * Get app path
     */
    public function getAppPath() {
        return $this->appPath;
    }
    
    /**
     * Handle routing
     */
    public function handleRoute($route) {
        $routes = $this->config['routes'] ?? [];
        
        if (isset($routes[$route])) {
            $templatePath = $this->appPath . '/' . $routes[$route];
            if (file_exists($templatePath)) {
                return $templatePath;
            }
        }
        
        // Default to main template
        return $this->appPath . '/templates/main.php';
    }
    
    /**
     * Get app status
     */
    public function getStatus() {
        return [
            'version' => $this->config['app']['version'],
            'status' => $this->config['app']['status'],
            'health' => $this->getHealthStatus(),
            'features' => array_keys(array_filter($this->config['features'])),
            'last_activity' => $this->getLastActivity()
        ];
    }
    
    /**
     * Get health status
     */
    private function getHealthStatus() {
        $checks = [
            'config' => file_exists($this->appPath . '/config.json') ? 'ok' : 'error',
            'data_storage' => is_writable($this->appPath . '/data') ? 'ok' : 'error',
            'ai_integration' => $this->aiIntegration->testConnection() ? 'ok' : 'warning'
        ];
        
        $overallStatus = in_array('error', $checks) ? 'unhealthy' : 
                        (in_array('warning', $checks) ? 'warning' : 'healthy');
        
        return [
            'status' => $overallStatus,
            'checks' => $checks
        ];
    }
    
    /**
     * Get last activity timestamp
     */
    private function getLastActivity() {
        // This would typically read from activity logs
        return date('Y-m-d H:i:s');
    }
    
    /**
     * Log activity
     */
    public function logActivity($action, $description, $data = []) {
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'action' => $action,
            'description' => $description,
            'data' => $data,
            'user' => $_SESSION['user']['username'] ?? 'system'
        ];
        
        $this->dataStorage->appendToLog('activity', $logEntry);
    }
    
    /**
     * Get bricks
     */
    public function getBricks($userId = null) {
        $userId = $userId ?? ($_SESSION['user']['id'] ?? 'default');
        return $this->dataStorage->loadUserData($userId, 'bricks') ?? [];
    }
    
    /**
     * Save brick
     */
    public function saveBrick($brick, $userId = null) {
        $userId = $userId ?? ($_SESSION['user']['id'] ?? 'default');
        
        // Generate ID if not provided
        if (!isset($brick['id'])) {
            $brick['id'] = 'brick-' . uniqid();
        }
        
        // Add metadata
        $brick['created_at'] = date('Y-m-d H:i:s');
        $brick['updated_at'] = date('Y-m-d H:i:s');
        
        $bricks = $this->getBricks($userId);
        
        // Update existing or add new
        $found = false;
        foreach ($bricks as &$existingBrick) {
            if ($existingBrick['id'] === $brick['id']) {
                $brick['created_at'] = $existingBrick['created_at']; // Preserve creation date
                $existingBrick = $brick;
                $found = true;
                break;
            }
        }
        
        if (!$found) {
            $bricks[] = $brick;
        }
        
        $this->dataStorage->saveUserData($userId, 'bricks', $bricks);
        $this->logActivity('brick.saved', 'Brick saved', ['brick_id' => $brick['id']]);
        
        return $brick;
    }
    
    /**
     * Delete brick
     */
    public function deleteBrick($brickId, $userId = null) {
        $userId = $userId ?? ($_SESSION['user']['id'] ?? 'default');
        
        $bricks = $this->getBricks($userId);
        $bricks = array_filter($bricks, function($brick) use ($brickId) {
            return $brick['id'] !== $brickId;
        });
        
        $this->dataStorage->saveUserData($userId, 'bricks', array_values($bricks));
        $this->logActivity('brick.deleted', 'Brick deleted', ['brick_id' => $brickId]);
        
        return true;
    }
    
    /**
     * Generate story using AI
     */
    public function generateStory($prompt, $bricks = [], $userId = null) {
        $userId = $userId ?? ($_SESSION['user']['id'] ?? 'default');
        
        try {
            $story = $this->aiIntegration->generateStory($prompt, $bricks);
            
            // Save generated story
            $storyData = [
                'id' => 'story-' . uniqid(),
                'prompt' => $prompt,
                'bricks' => $bricks,
                'content' => $story,
                'created_at' => date('Y-m-d H:i:s'),
                'user_id' => $userId
            ];
            
            $this->saveStory($storyData, $userId);
            $this->logActivity('story.generated', 'Story generated using AI', ['story_id' => $storyData['id']]);
            
            return $story;
        } catch (Exception $e) {
            $this->logActivity('story.generation_failed', 'Story generation failed', ['error' => $e->getMessage()]);
            throw $e;
        }
    }
    
    /**
     * Save story
     */
    public function saveStory($story, $userId = null) {
        $userId = $userId ?? ($_SESSION['user']['id'] ?? 'default');
        
        $stories = $this->getStories($userId);
        $stories[] = $story;
        
        $this->dataStorage->saveUserData($userId, 'stories', $stories);
        
        return $story;
    }
    
    /**
     * Get stories
     */
    public function getStories($userId = null) {
        $userId = $userId ?? ($_SESSION['user']['id'] ?? 'default');
        return $this->dataStorage->loadUserData($userId, 'stories') ?? [];
    }
    
    /**
     * Get app logs
     */
    public function getAppLogs($limit = 100) {
        return $this->dataStorage->getLogEntries('activity', $limit);
    }
    
    /**
     * Clear logs
     */
    public function clearLogs() {
        $this->dataStorage->clearLogs();
        $this->logActivity('logs.cleared', 'App logs cleared');
    }
}

// Initialize the app instance
$GLOBALS['generative_story_bible_app'] = new GenerativeStoryBibleApp();
?>
