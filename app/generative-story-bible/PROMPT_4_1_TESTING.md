# Testing Guide for Prompt 4.1: Create Drop Zones and Handle Drops

This guide helps you test the new drag-and-drop prompt assembler functionality.

## What Was Implemented

### 1. HTML Changes
- **Character Slot**: Drop zone for Character bricks with `data-slot-type="Character"`
- **Setting Slot**: Drop zone for Setting bricks with `data-slot-type="Setting"`
- Styled with dashed borders and hover effects

### 2. JavaScript State Management
- Added `state.assembler` object with `Character` and `Setting` properties
- Each slot can hold one brick ID or `null`

### 3. Drag and Drop Logic
- Event delegation for assembler drop zones
- Type validation (Character bricks only go in Character slot, etc.)
- Visual feedback during drag operations
- Automatic prompt preview updates

### 4. Rendering System
- `renderAssembler()` function updates slot contents
- Mini-cards show placed bricks with remove buttons
- `updateAssemblerPromptPreview()` builds final prompt

## Testing Steps

### Step 1: Verify Drop Zones
1. **Open the app and check the center column**
2. **You should see two drop zones:**
   - "Drop Character Brick Here" (top)
   - "Drop Setting Brick Here" (bottom)
3. **Visual appearance:**
   - Dashed borders (slate-600)
   - Proper spacing and padding
   - Hover effects (border changes to slate-500)

### Step 2: Test Character Brick Placement
1. **Create a Character brick** using the modal:
   ```
   Brick Name: Test Hero
   Archetype: Warrior
   Backstory: A brave fighter
   Goal: Save the kingdom
   ```

2. **Drag the Character brick to the Character slot**
   - Should show drag-over visual feedback
   - Should accept the drop
   - Should display mini-card with brick info
   - Should show remove button (×)

3. **Try dragging Character brick to Setting slot**
   - Should show console message: "Type mismatch: Cannot place character brick in Setting slot"
   - Should not place the brick

### Step 3: Test Setting Brick (if available)
**Note**: Currently only Character bricks can be created via the modal. For testing Setting bricks, you can:

1. **Modify sample data** or create a Setting brick manually in console:
   ```javascript
   // In browser console:
   const settingBrick = {
     id: 'setting-test',
     brickName: 'Dark Forest',
     brickType: 'Setting',
     promptText: 'A mysterious dark forest filled with ancient magic.'
   };
   state.bricks.push(settingBrick);
   renderBrickLibrary();
   ```

2. **Test Setting brick placement**
   - Should only go in Setting slot
   - Should be rejected by Character slot

### Step 4: Test Prompt Preview
1. **Place a Character brick in the Character slot**
2. **Check the prompt preview textarea (bottom of center column)**
3. **Should show:**
   ```
   Create a story incorporating these elements:

   The character is Test Hero, a Warrior. Backstory: A brave fighter. Their primary goal is: Save the kingdom.
   ```

4. **Add a Setting brick (if available)**
5. **Prompt should update to include both elements**

### Step 5: Test Remove Functionality
1. **Place a brick in a slot**
2. **Click the × button on the mini-card**
3. **Slot should reset to "Drop [Type] Brick Here"**
4. **Prompt preview should update**

### Step 6: Test Story Generation
1. **Place at least one brick in the assembler**
2. **Click "Generate Story"**
3. **Should work with new assembler system**
4. **Should use bricks from assembler slots**

## Expected Visual States

### Empty Slots
```html
<div id="character-slot" class="border-2 border-dashed border-slate-600 rounded-lg p-6 text-center transition-all duration-200 hover:border-slate-500 hover:bg-slate-800/50" data-slot-type="Character">
    <p class="text-slate-400">Drop Character Brick Here</p>
</div>
```

### Filled Slots
```html
<div id="character-slot" class="border-2 border-solid border-slate-500 rounded-lg p-6 text-center transition-all duration-200" data-slot-type="Character">
    <div class="bg-slate-700 border border-slate-500 rounded-lg p-3">
        <div class="flex justify-between items-start mb-2">
            <h4 class="font-medium text-slate-100 text-sm">Test Hero</h4>
            <button class="text-slate-400 hover:text-red-400 text-sm" onclick="removeFromSlot('Character')">×</button>
        </div>
        <p class="text-xs text-slate-400">character</p>
    </div>
</div>
```

### Drag Over State
- Border changes to blue (`border-blue-500`)
- Background becomes semi-transparent blue
- Slight scale transform

## Console Testing

### Check State
```javascript
// In browser console:
console.log('Assembler state:', state.assembler);
console.log('Available bricks:', state.bricks);
```

### Manual Brick Placement
```javascript
// Place a brick manually:
state.assembler.Character = 'brick-1';
renderAssembler();
```

### Check Prompt Generation
```javascript
// Trigger prompt update:
updateAssemblerPromptPreview();
console.log('Prompt:', document.getElementById('final-prompt-preview').value);
```

## Common Issues and Solutions

### Bricks Not Dropping
- Check console for error messages
- Verify brick types match slot types
- Ensure event listeners are attached

### Visual Feedback Not Working
- Check CSS classes are applied
- Verify drag enter/leave handlers
- Test hover states manually

### Prompt Not Updating
- Check `updateAssemblerPromptPreview()` is called
- Verify brick data has `promptText` property
- Check textarea element reference

### Remove Button Not Working
- Verify `removeFromSlot()` is globally accessible
- Check function is called with correct slot type
- Ensure `renderAssembler()` is called after removal

## Success Criteria

✅ **Drop Zones**: Two distinct slots for Character and Setting bricks
✅ **Visual Feedback**: Hover and drag-over states work correctly
✅ **Type Validation**: Only matching brick types can be placed in slots
✅ **State Management**: `state.assembler` tracks placed bricks
✅ **Rendering**: Slots show mini-cards when filled
✅ **Remove Functionality**: × buttons remove bricks from slots
✅ **Prompt Preview**: Updates automatically when assembler changes
✅ **Story Generation**: Works with new assembler system
✅ **Console Logging**: Proper debug information for validation

## Sample Test Data

**Character Brick:**
```javascript
{
  id: 'char-1',
  brickName: 'Brave Knight',
  brickType: 'Character',
  promptText: 'The character is Brave Knight, a Noble Warrior. Backstory: Trained in the royal academy. Their primary goal is: Protect the innocent.'
}
```

**Setting Brick:**
```javascript
{
  id: 'set-1',
  brickName: 'Ancient Castle',
  brickType: 'Setting',
  promptText: 'An ancient castle perched on a cliff, filled with mysterious corridors and hidden secrets.'
}
```

## Next Steps

After successful testing:
1. Verify all drag and drop functionality works
2. Test with multiple brick types
3. Ensure real-time updates work properly
4. Ready for additional brick types and features

## Troubleshooting

**If slots don't accept drops:**
- Check `data-slot-type` attributes
- Verify event delegation is working
- Test drag data transfer manually

**If visual feedback is missing:**
- Ensure CSS classes are loaded
- Check hover and drag-over styles
- Verify event handlers are attached

**If prompt doesn't update:**
- Check `updateAssemblerPromptPreview()` function
- Verify brick data structure
- Test manual prompt generation
