{"app": {"id": "generative-story-bible", "name": "Generative Story Bible", "description": "A collaborative tool for building and managing story elements (Bricks) with AI-powered story generation", "version": "1.0.0", "author": "AI Dashboard Team", "license": "MIT", "homepage": "https://github.com/ai-dashboard/generative-story-bible", "status": "development"}, "dashboard": {"min_version": "1.0.0", "integration_version": "1.0.0", "requires_auth": true, "requires_admin": false, "menu_item": {"title": "Story Bible", "icon": "book-story", "position": 110, "visible": true}}, "features": {"ai_integration": true, "chat_interface": false, "flat_file_storage": true, "api_endpoints": true, "real_time": false, "notifications": true, "drag_drop": true, "firebase_integration": true}, "ai": {"models": ["gpt-4", "gpt-3.5-turbo", "claude-3", "gemini-pro"], "default_model": "gemini-pro", "max_tokens": 8000, "temperature": 0.7, "system_prompt": "You are a creative writing assistant specializing in story generation. You help users create compelling narratives by combining story elements (called 'Bricks') into cohesive stories. Focus on narrative flow, character development, and engaging storytelling."}, "storage": {"type": "flat_file", "directories": [{"name": "bricks", "description": "Story building blocks and elements"}, {"name": "stories", "description": "Generated stories and narratives"}, {"name": "user_data", "description": "User-specific app data"}]}, "api": {"endpoints": [{"path": "/api/bricks.php", "method": "GET|POST|PUT|DELETE", "description": "Brick management endpoint", "auth_required": true}, {"path": "/api/stories.php", "method": "GET|POST", "description": "Story generation and management", "auth_required": true}, {"path": "/api/ai-generate.php", "method": "POST", "description": "AI story generation endpoint", "auth_required": true}]}, "permissions": {"view": ["user", "admin"], "edit": ["user", "admin"], "admin": ["admin"], "api": ["user", "admin"]}, "backup": {"include_files": true, "include_data": true, "exclude_patterns": ["logs/*", "temp/*", "cache/*"]}, "logging": {"level": "info", "max_file_size": "10MB", "retention_days": 30, "categories": ["bricks", "stories", "ai", "api", "auth", "error"]}, "assets": {"css": ["assets/css/app.css"], "js": ["assets/js/app.js", "assets/js/bricks.js", "assets/js/story-generator.js"]}, "routes": {"/": "templates/main.php", "/bricks": "templates/bricks.php", "/stories": "templates/stories.php", "/settings": "templates/settings.php"}}