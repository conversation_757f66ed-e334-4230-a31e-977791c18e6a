<?php
/**
 * Generative Story Bible - Activity Logging API
 * 
 * This API handles activity logging for the app.
 */

// Include dashboard configuration and authentication
require_once __DIR__ . '/../../../config.php';
require_once __DIR__ . '/../../../auth.php';

// Require authentication
requireAuth();

// Include app files
require_once __DIR__ . '/../includes/app.php';

// Set JSON response header
header('Content-Type: application/json');

// Get app instance
$app = $GLOBALS['generative_story_bible_app'];

// Get request method and data
$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    switch ($method) {
        case 'POST':
            handleLogActivity($app, $input);
            break;
            
        case 'GET':
            handleGetActivity($app);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

/**
 * Handle activity logging
 */
function handleLogActivity($app, $input) {
    if (!$input || !isset($input['action'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Missing action in request']);
        return;
    }
    
    $action = $input['action'];
    $description = $input['description'] ?? '';
    $data = $input['data'] ?? [];
    
    // Auto-generate description if not provided
    if (empty($description)) {
        $description = generateDescriptionFromAction($action, $data);
    }
    
    $app->logActivity($action, $description, $data);
    
    echo json_encode(['success' => true, 'message' => 'Activity logged']);
}

/**
 * Handle getting activity logs
 */
function handleGetActivity($app) {
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 100;
    $limit = max(1, min(1000, $limit)); // Clamp between 1 and 1000
    
    $logs = $app->getAppLogs($limit);
    
    echo json_encode([
        'success' => true,
        'logs' => $logs,
        'count' => count($logs)
    ]);
}

/**
 * Generate description from action
 */
function generateDescriptionFromAction($action, $data) {
    switch ($action) {
        case 'app.viewed':
            return 'User viewed the app';
            
        case 'app.initialized':
            return 'App was initialized';
            
        case 'brick.created':
            return 'New brick was created';
            
        case 'brick.updated':
            return 'Brick was updated';
            
        case 'brick.deleted':
            return 'Brick was deleted';
            
        case 'story.generated':
            return 'Story was generated using AI';
            
        case 'story.saved':
            return 'Story was saved';
            
        case 'prompt.assembled':
            return 'Prompt was assembled from bricks';
            
        case 'bricks.loaded':
            return 'Bricks were loaded from storage';
            
        case 'ai.request':
            return 'AI request was made';
            
        case 'ai.response':
            return 'AI response was received';
            
        case 'error.occurred':
            return 'An error occurred';
            
        default:
            return 'User action: ' . $action;
    }
}
?>
