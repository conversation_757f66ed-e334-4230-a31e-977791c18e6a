<?php
/**
 * Generative Story Bible - Bricks API
 * 
 * This API handles CRUD operations for story bricks.
 */

// Include dashboard configuration and authentication
require_once __DIR__ . '/../../../config.php';
require_once __DIR__ . '/../../../auth.php';

// Require authentication
requireAuth();

// Include app files
require_once __DIR__ . '/../includes/app.php';

// Set JSON response header
header('Content-Type: application/json');

// Get app instance
$app = $GLOBALS['generative_story_bible_app'];

// Get request method and data
$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    switch ($method) {
        case 'GET':
            handleGetBricks($app);
            break;
            
        case 'POST':
            handleCreateBrick($app, $input);
            break;
            
        case 'PUT':
            handleUpdateBrick($app, $input);
            break;
            
        case 'DELETE':
            handleDeleteBrick($app, $input);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

/**
 * Handle GET request - retrieve bricks
 */
function handleGetBricks($app) {
    $userId = $_SESSION['user']['id'] ?? 'default';
    $bricks = $app->getBricks($userId);
    
    echo json_encode($bricks);
}

/**
 * Handle POST request - create new brick
 */
function handleCreateBrick($app, $input) {
    if (!$input) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid input data']);
        return;
    }
    
    // Validate required fields
    $requiredFields = ['title', 'content', 'type'];
    foreach ($requiredFields as $field) {
        if (!isset($input[$field]) || empty(trim($input[$field]))) {
            http_response_code(400);
            echo json_encode(['error' => "Missing required field: {$field}"]);
            return;
        }
    }
    
    // Sanitize input
    $brick = [
        'title' => trim($input['title']),
        'content' => trim($input['content']),
        'type' => trim($input['type']),
        'tags' => isset($input['tags']) ? array_map('trim', $input['tags']) : []
    ];
    
    // Validate type
    $validTypes = ['character', 'setting', 'plot', 'object', 'conflict', 'theme', 'dialogue', 'other'];
    if (!in_array($brick['type'], $validTypes)) {
        $brick['type'] = 'other';
    }
    
    $userId = $_SESSION['user']['id'] ?? 'default';
    $savedBrick = $app->saveBrick($brick, $userId);
    
    http_response_code(201);
    echo json_encode($savedBrick);
}

/**
 * Handle PUT request - update existing brick
 */
function handleUpdateBrick($app, $input) {
    if (!$input || !isset($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid input data or missing brick ID']);
        return;
    }
    
    // Validate required fields
    $requiredFields = ['title', 'content', 'type'];
    foreach ($requiredFields as $field) {
        if (!isset($input[$field]) || empty(trim($input[$field]))) {
            http_response_code(400);
            echo json_encode(['error' => "Missing required field: {$field}"]);
            return;
        }
    }
    
    // Sanitize input
    $brick = [
        'id' => $input['id'],
        'title' => trim($input['title']),
        'content' => trim($input['content']),
        'type' => trim($input['type']),
        'tags' => isset($input['tags']) ? array_map('trim', $input['tags']) : []
    ];
    
    // Validate type
    $validTypes = ['character', 'setting', 'plot', 'object', 'conflict', 'theme', 'dialogue', 'other'];
    if (!in_array($brick['type'], $validTypes)) {
        $brick['type'] = 'other';
    }
    
    $userId = $_SESSION['user']['id'] ?? 'default';
    
    // Check if brick exists
    $existingBricks = $app->getBricks($userId);
    $brickExists = false;
    foreach ($existingBricks as $existingBrick) {
        if ($existingBrick['id'] === $brick['id']) {
            $brickExists = true;
            break;
        }
    }
    
    if (!$brickExists) {
        http_response_code(404);
        echo json_encode(['error' => 'Brick not found']);
        return;
    }
    
    $savedBrick = $app->saveBrick($brick, $userId);
    
    echo json_encode($savedBrick);
}

/**
 * Handle DELETE request - delete brick
 */
function handleDeleteBrick($app, $input) {
    $brickId = null;
    
    // Get brick ID from input or query parameter
    if ($input && isset($input['id'])) {
        $brickId = $input['id'];
    } elseif (isset($_GET['id'])) {
        $brickId = $_GET['id'];
    }
    
    if (!$brickId) {
        http_response_code(400);
        echo json_encode(['error' => 'Missing brick ID']);
        return;
    }
    
    $userId = $_SESSION['user']['id'] ?? 'default';
    
    // Check if brick exists
    $existingBricks = $app->getBricks($userId);
    $brickExists = false;
    foreach ($existingBricks as $existingBrick) {
        if ($existingBrick['id'] === $brickId) {
            $brickExists = true;
            break;
        }
    }
    
    if (!$brickExists) {
        http_response_code(404);
        echo json_encode(['error' => 'Brick not found']);
        return;
    }
    
    $result = $app->deleteBrick($brickId, $userId);
    
    if ($result) {
        echo json_encode(['success' => true, 'message' => 'Brick deleted successfully']);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to delete brick']);
    }
}
?>
