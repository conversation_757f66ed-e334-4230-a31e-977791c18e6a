<?php
/**
 * Generative Story Bible - AI Generation API
 * 
 * This API handles AI-powered story generation.
 */

// Include dashboard configuration and authentication
require_once __DIR__ . '/../../../config.php';
require_once __DIR__ . '/../../../auth.php';

// Require authentication
requireAuth();

// Include app files
require_once __DIR__ . '/../includes/app.php';

// Set JSON response header
header('Content-Type: application/json');

// Get app instance
$app = $GLOBALS['generative_story_bible_app'];

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Get request data
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid input data']);
    exit;
}

try {
    $action = $input['action'] ?? 'generate_story';
    
    switch ($action) {
        case 'generate_story':
            handleGenerateStory($app, $input);
            break;
            
        case 'suggest_bricks':
            handleSuggestBricks($app, $input);
            break;
            
        case 'analyze_story':
            handleAnalyzeStory($app, $input);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

/**
 * Handle story generation
 */
function handleGenerateStory($app, $input) {
    if (!isset($input['prompt']) || empty(trim($input['prompt']))) {
        http_response_code(400);
        echo json_encode(['error' => 'Missing or empty prompt']);
        return;
    }
    
    $prompt = trim($input['prompt']);
    $bricks = $input['bricks'] ?? [];
    $userId = $_SESSION['user']['id'] ?? 'default';
    
    try {
        $story = $app->generateStory($prompt, $bricks, $userId);
        
        echo json_encode([
            'success' => true,
            'story' => $story,
            'prompt' => $prompt,
            'bricks_used' => count($bricks),
            'generated_at' => date('Y-m-d H:i:s')
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Story generation failed: ' . $e->getMessage()]);
    }
}

/**
 * Handle brick suggestions
 */
function handleSuggestBricks($app, $input) {
    $existingBricks = $input['existing_bricks'] ?? [];
    $theme = $input['theme'] ?? '';
    
    try {
        // Get AI integration instance
        $aiIntegration = new StoryBibleAIIntegration($app->getConfig()['ai']);
        $suggestions = $aiIntegration->suggestBricks($existingBricks, $theme);
        
        echo json_encode([
            'success' => true,
            'suggestions' => $suggestions,
            'theme' => $theme,
            'based_on_bricks' => count($existingBricks)
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Brick suggestion failed: ' . $e->getMessage()]);
    }
}

/**
 * Handle story analysis
 */
function handleAnalyzeStory($app, $input) {
    if (!isset($input['story']) || empty(trim($input['story']))) {
        http_response_code(400);
        echo json_encode(['error' => 'Missing or empty story']);
        return;
    }
    
    $story = trim($input['story']);
    $bricks = $input['bricks'] ?? [];
    
    try {
        // Get AI integration instance
        $aiIntegration = new StoryBibleAIIntegration($app->getConfig()['ai']);
        $analysis = $aiIntegration->analyzeStory($story, $bricks);
        
        echo json_encode([
            'success' => true,
            'analysis' => $analysis,
            'story_length' => str_word_count($story),
            'bricks_analyzed' => count($bricks),
            'analyzed_at' => date('Y-m-d H:i:s')
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Story analysis failed: ' . $e->getMessage()]);
    }
}
?>
