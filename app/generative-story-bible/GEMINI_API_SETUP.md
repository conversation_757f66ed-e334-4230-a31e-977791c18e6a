# Gemini API Setup Guide

This guide helps you configure the Gemini API for AI story generation in the Generative Story Bible app.

## Getting Your Gemini API Key

### Step 1: Access Google AI Studio
1. Go to [Google AI Studio](https://aistudio.google.com/)
2. Sign in with your Google account
3. Accept the terms of service if prompted

### Step 2: Create API Key
1. Click on "Get API key" in the left sidebar
2. Click "Create API key"
3. Select "Create API key in new project" or choose an existing project
4. Copy the generated API key
5. **Important**: Store this key securely and never share it publicly

## Configuration Options

### Option 1: Dashboard Configuration (Recommended)
If your AI Dashboard supports Gemini API configuration:

1. Go to your dashboard settings
2. Look for "AI Configuration" or "API Keys" section
3. Add your Gemini API key
4. The app will automatically use `window.dashboardConfig.geminiApiKey`

### Option 2: Environment Variable
For server environments that support environment variables:

1. Set the environment variable:
   ```bash
   export GEMINI_API_KEY="your-api-key-here"
   ```
2. The app will try to access `process.env.GEMINI_API_KEY`

### Option 3: Development/Testing (Temporary)
For development and testing only:

1. Open `assets/js/app.js`
2. Find the `getGeminiApiKey()` function
3. Replace the empty `devApiKey` with your API key:
   ```javascript
   const devApiKey = "your-api-key-here"; // Set your API key here for testing
   ```
4. **Warning**: Never commit this to version control!

## API Configuration Details

### Model Used
- **Model**: `gemini-2.0-flash`
- **Endpoint**: `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent`

### Request Format
```javascript
{
  contents: [{
    role: "user",
    parts: [{ text: "Your story prompt here" }]
  }]
}
```

### Response Format
The app expects this response structure:
```javascript
{
  candidates: [{
    content: {
      parts: [{
        text: "Generated story content"
      }]
    }
  }]
}
```

## Testing Your Setup

### Step 1: Verify Configuration
1. Open the Generative Story Bible app
2. Open browser developer console
3. Try generating a story
4. Check for any API key related errors

### Step 2: Test API Call
1. Create a character brick
2. Place it in the assembler
3. Click "Generate Story"
4. Should see "Generating..." loading state
5. Should receive generated story or error message

### Expected Console Messages
**Success:**
```
Story generation completed successfully
```

**API Key Missing:**
```
Error generating story: Gemini API key not configured. Please set up your API key in the dashboard configuration.
```

**API Error:**
```
Error generating story: API request failed with status 401
```

## Troubleshooting

### Common Issues

#### 1. "API key not configured" Error
**Solution**: Ensure your API key is set using one of the configuration options above.

#### 2. "API request failed with status 401" Error
**Cause**: Invalid or expired API key
**Solution**: 
- Verify your API key is correct
- Check if the API key has proper permissions
- Generate a new API key if needed

#### 3. "API request failed with status 403" Error
**Cause**: API quota exceeded or billing not enabled
**Solution**:
- Check your Google Cloud billing account
- Verify API quotas in Google Cloud Console
- Enable billing if required

#### 4. "Invalid response format" Error
**Cause**: Unexpected response structure from API
**Solution**:
- Check if the Gemini API format has changed
- Verify the model name is correct
- Check network connectivity

### Debug Steps

#### 1. Check API Key
```javascript
// In browser console:
console.log('API Key configured:', !!getGeminiApiKey());
```

#### 2. Test API Manually
```javascript
// In browser console:
fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=YOUR_API_KEY', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    contents: [{
      role: "user",
      parts: [{ text: "Write a short story about a brave knight." }]
    }]
  })
}).then(r => r.json()).then(console.log);
```

#### 3. Check Network
- Verify internet connectivity
- Check if corporate firewall blocks Google APIs
- Test from different network if needed

## Security Best Practices

### 1. API Key Protection
- Never commit API keys to version control
- Use environment variables or secure configuration
- Rotate API keys regularly
- Restrict API key permissions if possible

### 2. Rate Limiting
- Implement client-side rate limiting if needed
- Monitor API usage in Google Cloud Console
- Set up billing alerts

### 3. Error Handling
- Don't expose API keys in error messages
- Log errors securely on server side
- Provide user-friendly error messages

## Production Deployment

### Recommended Setup
1. **Server-side API calls**: Move API calls to backend for better security
2. **Environment variables**: Store API key in secure environment variables
3. **Rate limiting**: Implement proper rate limiting
4. **Monitoring**: Set up API usage monitoring
5. **Fallback**: Implement fallback mechanisms for API failures

### Dashboard Integration
If integrating with AI Dashboard:
1. Add Gemini API configuration to dashboard settings
2. Use dashboard's secure configuration system
3. Implement proper user permissions
4. Add API usage tracking

## Support

### Google AI Studio
- Documentation: https://ai.google.dev/docs
- Support: https://developers.google.com/support

### Gemini API
- API Reference: https://ai.google.dev/api/rest
- Pricing: https://ai.google.dev/pricing
- Quotas: https://ai.google.dev/docs/quota

### Troubleshooting
1. Check Google AI Studio status page
2. Verify API key permissions
3. Review Google Cloud Console logs
4. Test with minimal example
5. Contact Google support if needed

## Alternative Configuration

If you prefer to use a different AI model or API:

1. Modify the `generateStory()` function in `app.js`
2. Update the API endpoint and request format
3. Adjust the response parsing logic
4. Update error handling as needed

The app is designed to be flexible and can be adapted to work with other AI APIs with minimal changes.
