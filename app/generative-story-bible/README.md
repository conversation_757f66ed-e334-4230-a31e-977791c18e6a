# Generative Story Bible

A collaborative tool for building and managing story elements (Bricks) with AI-powered story generation.

## Overview

The Generative Story Bible is an innovative storytelling application that allows users to create, manage, and combine story elements called "Bricks" to generate compelling narratives using AI. The app features a drag-and-drop interface built with vanilla JavaScript, Tailwind CSS, and integrates seamlessly with the AI Dashboard framework.

## Features

### Core Functionality
- **Brick Library**: Create and manage reusable story elements (characters, settings, plots, etc.)
- **Drag & Drop Interface**: Intuitive three-column layout for assembling story prompts
- **AI Story Generation**: Generate stories using assembled bricks and AI models
- **Story Archive**: Save and manage generated stories
- **Collaborative Elements**: Share and reuse bricks across projects

### Technical Features
- **Vanilla JavaScript (ES6 Modules)**: Modern, framework-free frontend
- **Tailwind CSS**: Responsive, utility-first styling
- **AI Dashboard Integration**: Seamless authentication and theme integration
- **Flat File Storage**: Reliable data persistence without database dependencies
- **Firebase Firestore Ready**: Prepared for cloud storage integration
- **RESTful API**: Clean API endpoints for all operations

## Architecture

### Frontend Structure
```
assets/
├── css/
│   └── app.css          # Custom styles complementing Tailwind
├── js/
│   ├── app.js           # Main application logic
│   ├── bricks.js        # Brick management (future)
│   └── story-generator.js # Story generation (future)
└── images/              # App-specific images
```

### Backend Structure
```
includes/
├── app.php              # Main app class
├── data-storage.php     # Flat file data operations
└── ai-integration.php   # AI API integration

api/
├── bricks.php           # Brick CRUD operations
├── ai-generate.php      # AI story generation
└── activity.php         # Activity logging

templates/
└── main.php             # Main application interface

data/
├── bricks/              # Brick storage
├── stories/             # Generated stories
├── user_data/           # User-specific data
└── logs/                # Activity logs
```

## Installation

1. **Copy to Apps Directory**: Place the `generative-story-bible` folder in your AI Dashboard's `app/` directory.

2. **Verify Permissions**: Ensure the `data/` directory is writable:
   ```bash
   chmod 755 app/generative-story-bible/data
   ```

3. **Access via Dashboard**: The app will automatically appear in your AI Dashboard menu.

## Usage

### Creating Bricks
1. Click the "+ New Brick" button in the left panel
2. Fill in the brick details:
   - **Title**: Short, descriptive name
   - **Type**: character, setting, plot, object, conflict, theme, dialogue, or other
   - **Content**: Detailed description of the story element
   - **Tags**: Keywords for organization and search

### Building Stories
1. Drag bricks from the library to the prompt assembler
2. Arrange bricks in your desired order
3. Review the assembled prompt in the preview area
4. Click "Generate Story" to create your narrative

### Managing Content
- **Edit Bricks**: Click on any brick to modify its content
- **Delete Bricks**: Use the delete button on brick cards
- **View Stories**: Access your story archive through the dashboard
- **Export**: Copy generated stories for use in other applications

## API Reference

### Bricks API (`/api/bricks.php`)
- `GET`: Retrieve all user bricks
- `POST`: Create new brick
- `PUT`: Update existing brick
- `DELETE`: Remove brick

### AI Generation API (`/api/ai-generate.php`)
- `POST`: Generate story from prompt and bricks
- `POST`: Suggest new bricks based on existing ones
- `POST`: Analyze generated stories for feedback

### Activity API (`/api/activity.php`)
- `POST`: Log user activities
- `GET`: Retrieve activity logs

## Configuration

The app configuration is managed through `config.json`:

```json
{
  "app": {
    "name": "Generative Story Bible",
    "version": "1.0.0"
  },
  "ai": {
    "default_model": "gemini-pro",
    "max_tokens": 8000,
    "temperature": 0.7
  },
  "features": {
    "ai_integration": true,
    "drag_drop": true,
    "firebase_integration": true
  }
}
```

## Development

### Adding New Features
1. **Frontend**: Add new JavaScript modules in `assets/js/`
2. **Backend**: Create new API endpoints in `api/`
3. **Templates**: Add new views in `templates/`
4. **Styling**: Extend styles in `assets/css/app.css`

### Testing
- Use the dashboard's built-in logging for debugging
- Test API endpoints with tools like Postman
- Verify responsive design across devices

### Deployment
- The app integrates with the dashboard's update system
- Backups are handled automatically
- Version management follows semantic versioning

## Integration

### AI Dashboard Features
- **Authentication**: Uses dashboard user system
- **Themes**: Inherits dashboard styling
- **Logging**: Integrated activity tracking
- **Updates**: Automatic update notifications
- **Backups**: Included in dashboard backups

### External Services
- **AI Models**: Supports multiple AI providers through dashboard configuration
- **Firebase**: Ready for Firestore integration
- **Export**: Compatible with standard document formats

## Troubleshooting

### Common Issues
1. **Bricks not loading**: Check data directory permissions
2. **AI generation failing**: Verify AI API configuration in dashboard
3. **Drag & drop not working**: Ensure JavaScript is enabled
4. **Styling issues**: Clear browser cache and check Tailwind CSS loading

### Logs
- App logs are stored in `data/logs/activity.json`
- Dashboard logs include app-specific entries
- Use browser developer tools for frontend debugging

## Future Enhancements

### Planned Features
- **Collaborative Editing**: Real-time brick sharing
- **Advanced AI Features**: Story analysis and suggestions
- **Export Options**: PDF, EPUB, and other formats
- **Templates**: Pre-built story structures
- **Version Control**: Track story iterations

### Technical Improvements
- **Performance**: Lazy loading for large brick libraries
- **Offline Support**: Service worker implementation
- **Mobile App**: Progressive Web App features
- **Integrations**: Third-party writing tools

## Support

- **Documentation**: Check the AI Dashboard documentation
- **Logs**: Review app and dashboard logs for errors
- **Community**: Use dashboard support channels
- **Updates**: Monitor for app updates through the dashboard

## License

MIT License - See the AI Dashboard license for full details.

## Contributing

Contributions are welcome! Please follow the AI Dashboard development guidelines and submit pull requests through the appropriate channels.
