<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generative Story Bible</title>
    
    <!-- Include Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Include Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body { font-family: 'Inter', sans-serif; }
        .drag-over { border: 2px dashed #3b82f6; background-color: rgba(59, 130, 246, 0.1); }
        .brick-card { transition: transform 0.2s, box-shadow 0.2s; }
        .brick-card:hover { transform: translateY(-2px); box-shadow: 0 8px 25px rgba(0,0,0,0.15); }
    </style>
</head>
<body class="bg-slate-900 text-slate-200 min-h-screen">
    <!-- App Header -->
    <div class="bg-slate-800 border-b border-slate-700 p-4">
        <div class="max-w-7xl mx-auto">
            <h1 class="text-2xl font-bold text-slate-100">📚 Generative Story Bible</h1>
            <p class="text-slate-400 text-sm">Create compelling stories by combining story elements with AI-powered generation</p>
        </div>
    </div>

    <!-- Main Application Interface -->
    <div class="max-w-7xl mx-auto p-4">
        <div class="h-screen flex gap-4">
            <!-- Left Column (Brick Library) -->
            <div id="brick-library-container" class="w-80 bg-slate-800 border border-slate-700 rounded-lg flex flex-col">
                <div class="p-4 border-b border-slate-700">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-semibold text-slate-100">My Bricks</h2>
                        <button id="add-new-brick-btn" class="bg-slate-700 hover:bg-slate-600 text-slate-200 px-3 py-2 rounded-lg text-sm font-medium transition duration-200">
                            + New Brick
                        </button>
                    </div>
                </div>
                <div id="brick-library-list" class="flex-1 overflow-y-auto p-4 space-y-3">
                    <!-- Brick cards will be populated here -->
                </div>
            </div>

            <!-- Center Column (Prompt Assembler) -->
            <div id="prompt-assembler-container" class="flex-1 bg-slate-800 border border-slate-700 rounded-lg flex flex-col">
                <div class="p-4 border-b border-slate-700">
                    <h2 class="text-xl font-semibold text-slate-100">Prompt Assembler</h2>
                </div>
                <div class="flex-1 p-4 flex flex-col">
                    <div id="prompt-assembler-slots" class="flex-1 p-6 mb-4 min-h-64 space-y-4">
                        <!-- Character Slot -->
                        <div id="character-slot" 
                             class="border-2 border-dashed border-slate-600 rounded-lg p-6 text-center transition-all duration-200 hover:border-slate-500 hover:bg-slate-800/50"
                             data-slot-type="Character">
                            <p class="text-slate-400">Drop Character Brick Here</p>
                        </div>
                        
                        <!-- Setting Slot -->
                        <div id="setting-slot" 
                             class="border-2 border-dashed border-slate-600 rounded-lg p-6 text-center transition-all duration-200 hover:border-slate-500 hover:bg-slate-800/50"
                             data-slot-type="Setting">
                            <p class="text-slate-400">Drop Setting Brick Here</p>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <textarea 
                            id="final-prompt-preview" 
                            readonly 
                            class="w-full h-32 bg-slate-700 border border-slate-600 rounded-lg p-3 text-slate-200 resize-none"
                            placeholder="Your assembled prompt will appear here..."
                        ></textarea>
                        <button 
                            id="generate-story-btn" 
                            class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            Generate Story
                        </button>
                    </div>
                </div>
            </div>

            <!-- Right Column (AI Output) -->
            <div id="ai-output-container" class="flex-1 bg-slate-800 border border-slate-700 rounded-lg flex flex-col">
                <div class="p-4 border-b border-slate-700">
                    <h2 class="text-xl font-semibold text-slate-100">Generated Story</h2>
                </div>
                <div id="ai-output-content" class="flex-1 p-4 overflow-y-auto">
                    <div class="border border-slate-600 rounded-lg p-6 h-full">
                        <p class="text-slate-400">Your generated story will appear here...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Overlay -->
    <div id="modal-overlay" class="hidden fixed inset-0 bg-black/70 flex items-center justify-center z-50">
        <div id="brick-modal" class="bg-slate-800 rounded-lg p-6 max-w-md w-full mx-4 border border-slate-600">
            <h2 class="text-xl font-semibold text-slate-100 mb-4">Create New Brick</h2>
            
            <form id="brick-form" class="space-y-4">
                <!-- Brick Name -->
                <div>
                    <label for="brick-name-input" class="block text-sm font-medium text-slate-200 mb-1">
                        Brick Name
                    </label>
                    <input 
                        type="text" 
                        id="brick-name-input" 
                        required
                        class="w-full bg-slate-700 border border-slate-600 rounded-lg px-3 py-2 text-slate-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Enter brick name"
                    >
                </div>
                
                <!-- Character's Archetype -->
                <div>
                    <label for="character-archetype-input" class="block text-sm font-medium text-slate-200 mb-1">
                        Character's Archetype
                    </label>
                    <input 
                        type="text" 
                        id="character-archetype-input" 
                        required
                        class="w-full bg-slate-700 border border-slate-600 rounded-lg px-3 py-2 text-slate-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="e.g., Hero, Mentor, Trickster"
                    >
                </div>
                
                <!-- Character's Backstory -->
                <div>
                    <label for="character-backstory-input" class="block text-sm font-medium text-slate-200 mb-1">
                        Character's Backstory
                    </label>
                    <textarea 
                        id="character-backstory-input" 
                        required
                        rows="3"
                        class="w-full bg-slate-700 border border-slate-600 rounded-lg px-3 py-2 text-slate-200 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                        placeholder="Describe the character's background and history"
                    ></textarea>
                </div>
                
                <!-- Character's Goal -->
                <div>
                    <label for="character-goal-input" class="block text-sm font-medium text-slate-200 mb-1">
                        Character's Goal
                    </label>
                    <textarea 
                        id="character-goal-input" 
                        required
                        rows="2"
                        class="w-full bg-slate-700 border border-slate-600 rounded-lg px-3 py-2 text-slate-200 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                        placeholder="What does this character want to achieve?"
                    ></textarea>
                </div>
                
                <!-- Buttons -->
                <div class="flex space-x-3 pt-4">
                    <button 
                        type="submit" 
                        class="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200"
                    >
                        Save Brick
                    </button>
                    <button 
                        type="button" 
                        id="cancel-brick-btn" 
                        class="flex-1 bg-slate-600 hover:bg-slate-700 text-slate-200 font-medium py-2 px-4 rounded-lg transition duration-200"
                    >
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- App-specific JavaScript -->
    <script type="module" src="assets/js/app.js"></script>

    <!-- Simple initialization -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Generative Story Bible standalone version initialized');
    });
    </script>
</body>
</html>
